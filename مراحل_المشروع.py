from for_all import *
from ستايل import *
from ui_boton import *
from db import *
import qtawesome as qta
from datetime import datetime
import mysql.connector
from PySide6.QtWidgets import QInputDialog

class ProjectPhasesWindow(QDialog):
    """نافذة شاملة لإدارة مراحل المشروع"""
    
    def __init__(self, parent=None, project_data=None, project_type="تصميم"):
        super().__init__(parent)
        self.parent = parent
        self.project_data = project_data or {}
        self.project_type = project_type
        self.project_id = self.project_data.get('id', None)
        self.client_id = self.project_data.get('معرف_العميل', None)
        
        # إعداد النافذة الأساسية
        self.setup_window()
        
        # إنشاء التابات
        self.create_tabs()
        
        # تحميل البيانات
        self.load_project_info()
        
        # تطبيق الستايل
        apply_stylesheet(self)

    def add_auto_numbers_to_table(self, table):
        """إضافة أرقام تلقائية لجدول"""
        for row in range(table.rowCount()):
            auto_number_item = QTableWidgetItem(str(row + 1))
            auto_number_item.setTextAlignment(Qt.AlignCenter)
            table.setItem(row, 1, auto_number_item)  # العمود الثاني (index 1) هو عمود الرقم

    def apply_amount_status_color(self, item, status_text):
        """تطبيق تنسيق الألوان لعمود حالة المبلغ"""
        try:
            if status_text == "غير مدرج":
                # تطبيق اللون الأحمر للنص
                item.setForeground(QBrush(QColor(231, 76, 60)))
                # تطبيق الخط العريض
                font = QFont()
                font.setBold(True)
                item.setFont(font)
            elif status_text == "تم الإدراج":
                # تطبيق اللون الأخضر للنص
                item.setForeground(QBrush(QColor(46, 125, 50)))
                # تطبيق الخط العريض
                font = QFont()
                font.setBold(True)
                item.setFont(font)
            else:
                # إزالة أي تنسيق للحالات الأخرى
                item.setForeground(QBrush())
                font = QFont()
                font.setBold(False)
                item.setFont(font)
        except Exception as e:
            print(f"خطأ في تطبيق تنسيق الألوان: {e}")
        
    def setup_window(self):
        """إعداد النافذة الأساسية"""
        project_name = self.project_data.get('اسم_المشروع', 'مشروع جديد')
        self.setWindowTitle(f"إدارة المشروع - {project_name}")
        self.setGeometry(100, 100, 1600, 900)
        self.setLayoutDirection(Qt.RightToLeft)
        self.setWindowFlags(Qt.Window | Qt.WindowMaximizeButtonHint | Qt.WindowCloseButtonHint)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        
        # إنشاء التابات
        self.tab_widget = QTabWidget()
        main_layout.addWidget(self.tab_widget)
        
    def create_tabs(self):
        """إنشاء التابات حسب نوع المشروع"""
        # تاب معلومات المشروع (افتراضي)
        self.create_project_info_tab()
        
        # تاب مراحل المشروع
        self.create_project_phases_tab()
        
        # تاب مهام المهندسين
        self.create_engineers_tasks_tab()
        
        # تاب الجدول الزمني
        self.create_timeline_tab()
        
        # تاب التقارير الشاملة
        self.create_reports_tab()

        # تاب الملفات والمرفقات (متاح لجميع أنواع المشاريع)
        self.create_attachments_tab()

        # إضافة تابات خاصة بالمقاولات
        if self.project_type == "مقاولات":
            self.create_expenses_tab()
            self.create_custody_tab()
            self.create_custody_payments_tab()
            self.create_contractors_tab()
            self.create_workers_tab()
            self.create_suppliers_tab()

        # ربط إشارة تغيير التاب بدالة التحديث التلقائي
        self.tab_widget.currentChanged.connect(self.on_tab_changed)

    def create_project_info_tab(self):
        """إنشاء تاب معلومات المشروع"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setSpacing(15)
        
        # إنشاء scroll area للمحتوى
        scroll = QScrollArea()
        scroll.setWidgetResizable(True)
        scroll.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)
        content_layout.setSpacing(20)

        # تخطيط أفقي للحاويات الثلاث الأولى
        top_containers_layout = QHBoxLayout()
        top_containers_layout.setSpacing(15)

        # إنشاء حاويات فرعية للحاويات الثلاث الأولى
        basic_info_widget = QWidget()
        basic_info_layout = QVBoxLayout(basic_info_widget)
        basic_info_layout.setContentsMargins(0, 0, 0, 0)
        self.create_basic_info_container(basic_info_layout)

        financial_info_widget = QWidget()
        financial_info_layout = QVBoxLayout(financial_info_widget)
        financial_info_layout.setContentsMargins(0, 0, 0, 0)
        self.create_financial_info_container(financial_info_layout)

        timing_status_widget = QWidget()
        timing_status_layout = QVBoxLayout(timing_status_widget)
        timing_status_layout.setContentsMargins(0, 0, 0, 0)
        self.create_timing_status_container(timing_status_layout)

        # إضافة الحاويات الثلاث إلى التخطيط الأفقي
        top_containers_layout.addWidget(basic_info_widget)
        top_containers_layout.addWidget(financial_info_widget)
        top_containers_layout.addWidget(timing_status_widget)

        # إضافة التخطيط الأفقي إلى التخطيط الرئيسي
        content_layout.addLayout(top_containers_layout)

        # د. حاوية الوصف والملاحظات
        self.create_description_container(content_layout)

        # هـ. حاوية الإحصائيات المالية
        self.create_statistics_container(content_layout)
        
        scroll.setWidget(content_widget)
        layout.addWidget(scroll)
        
        self.tab_widget.addTab(tab, qta.icon('fa5s.info-circle', color='#3498db'), "معلومات المشروع")
        
    def create_basic_info_container(self, parent_layout):
        """إنشاء حاوية المعلومات الأساسية"""
        group = QGroupBox("المعلومات الأساسية")
        group.setStyleSheet("""
            QGroupBox {
                font-size: 14px;
                font-weight: bold;
                color: #2c3e50;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        
        layout = QGridLayout(group)
        layout.setSpacing(15)
        
        # اسم المشروع
        layout.addWidget(QLabel("اسم المشروع:"), 0, 0)
        self.project_name_label = QLabel()
        self.project_name_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #2c3e50;")
        layout.addWidget(self.project_name_label, 0, 1)
        
        # اسم المالك/العميل
        layout.addWidget(QLabel("اسم المالك/العميل:"), 1, 0)
        self.client_name_label = QLabel()
        self.client_name_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #2c3e50;")
        layout.addWidget(self.client_name_label, 1, 1)
        
        # المهندس المسؤول
        layout.addWidget(QLabel("المهندس المسؤول:"), 2, 0)
        self.engineer_name_label = QLabel()
        self.engineer_name_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #2c3e50;")
        layout.addWidget(self.engineer_name_label, 2, 1)

        # زر عرض الدفعات
        view_payments_btn = QPushButton("تعديل البيانات")
        view_payments_btn.setIcon(qta.icon('fa5s.list', color='white'))
        view_payments_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #5dade2;
            }
        """)
        view_payments_btn.clicked.connect(self.view_payments)
        layout.addWidget(view_payments_btn, 3, 0,2,2)
        
        parent_layout.addWidget(group)
        
    def create_financial_info_container(self, parent_layout):
        """إنشاء حاوية المعلومات المالية"""
        group = QGroupBox("المعلومات المالية")
        group.setStyleSheet("""
            QGroupBox {
                font-size: 14px;
                font-weight: bold;
                color: #2c3e50;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        
        layout = QGridLayout(group)
        layout.setSpacing(15)
        
        # إجمالي قيمة المشروع
        layout.addWidget(QLabel("إجمالي قيمة المشروع:"), 0, 0)
        self.total_amount_label = QLabel()
        self.total_amount_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #2c3e50;")
        layout.addWidget(self.total_amount_label, 0, 1)
        
        # إجمالي المبلغ المدفوع
        layout.addWidget(QLabel("إجمالي المبلغ المدفوع:"), 1, 0)
        self.paid_amount_label = QLabel()
        self.paid_amount_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #27ae60;")
        layout.addWidget(self.paid_amount_label, 1, 1)
        
        # إجمالي المبلغ المتبقي
        layout.addWidget(QLabel("إجمالي المبلغ المتبقي:"), 2, 0)
        self.remaining_amount_label = QLabel()
        self.remaining_amount_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #e74c3c;")
        layout.addWidget(self.remaining_amount_label, 2, 1)
        
        # أزرار العمليات المالية
        buttons_layout = QHBoxLayout()
        
        # زر إضافة دفعة جديدة
        add_payment_btn = QPushButton("دفعات المشروع")
        add_payment_btn.setIcon(qta.icon('fa5s.plus', color='white'))
        add_payment_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2ecc71;
            }
        """)
        add_payment_btn.clicked.connect(self.view_payments)
        buttons_layout.addWidget(add_payment_btn)
        
        
        
        #buttons_layout.addStretch()
        layout.addLayout(buttons_layout, 3, 0, 1, 2)
        
        parent_layout.addWidget(group)
        
    def load_project_info(self):
        """تحميل معلومات المشروع"""
        if not self.project_data:
            return

        # تحميل المعلومات الأساسية
        self.project_name_label.setText(self.project_data.get('اسم_المشروع', 'غير محدد'))

        # جلب اسم العميل من قاعدة البيانات
        client_name = self.get_client_name()
        self.client_name_label.setText(client_name)

        # جلب اسم المهندس من قاعدة البيانات
        engineer_name = self.get_engineer_name()
        self.engineer_name_label.setText(engineer_name)

        # تحميل المعلومات المالية
        total_amount = self.project_data.get('المبلغ', 0)
        paid_amount = self.project_data.get('المدفوع', 0)
        remaining_amount = self.project_data.get('الباقي', 0)

        self.total_amount_label.setText(f"{total_amount:,.2f} {Currency_type}")
        self.paid_amount_label.setText(f"{paid_amount:,.2f} {Currency_type}")
        self.remaining_amount_label.setText(f"{remaining_amount:,.2f} {Currency_type}")

        # تحميل معلومات التوقيت والحالة
        self.load_timing_status_info()

        # تحميل الوصف والملاحظات
        self.load_description_notes()

        # تحميل الإحصائيات
        self.load_statistics()

        # تحميل بيانات الجداول
        self.load_all_tables_data()

        # تحميل بيانات الفلاتر
        self.load_filter_data()

    def load_timing_status_info(self):
        """تحميل معلومات التوقيت والحالة"""
        start_date = self.project_data.get('تاريخ_الإستلام', '')
        end_date = self.project_data.get('تاريخ_التسليم', '')
        status = self.project_data.get('الحالة', 'غير محدد')

        self.start_date_label.setText(str(start_date) if start_date else 'غير محدد')
        self.end_date_label.setText(str(end_date) if end_date else 'غير محدد')
        self.project_status_label.setText(status)

        # حساب نسبة الإنجاز (بناءً على المراحل المكتملة)
        completion_percentage = self.calculate_completion_percentage()

        # حساب الوقت المتبقي
        if end_date:
            try:
                from datetime import datetime
                end_date_obj = datetime.strptime(str(end_date), '%Y-%m-%d')
                today = datetime.now()
                remaining_days = (end_date_obj - today).days

                if remaining_days > 0:
                    self.project_status_label.setText(f"{remaining_days} يوم   -   نسبة الإنجاز {completion_percentage}%")
                    self.project_status_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #f39c12;")
                elif remaining_days == 0:
                    self.project_status_label.setText("اليوم")
                    self.project_status_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #e67e22;")
                else:
                    self.project_status_label.setText(f"متأخر {abs(remaining_days)} يوم  -   نسبة الإنجاز {completion_percentage}%")
                    self.project_status_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #e74c3c;")
            except:
                self.project_status_label.setText("غير محدد")
                pass
        else:
            self.project_status_label.setText("غير محدد")
            pass


    def load_description_notes(self):
        """تحميل الوصف والملاحظات"""
        description = self.project_data.get('وصف_المشروع', '')
        notes = self.project_data.get('ملاحظات', '')

        self.project_description.setPlainText(description)
        self.project_notes.setPlainText(notes)

    def load_statistics(self):
        """تحميل الإحصائيات المالية"""
        try:
            # حساب عدد المراحل
            total_phases = self.get_total_phases_count()
            self.total_phases_label.setText(str(total_phases))

            # حساب إجمالي حسابات المهندسين
            total_engineers_amount = self.get_total_engineers_amount()
            self.total_engineers_amount_label.setText(f"{total_engineers_amount:,.2f} {Currency_type}")

            # حساب صافي ربح الشركة
            total_amount = self.project_data.get('المبلغ', 0)
            net_profit = total_amount - total_engineers_amount
            self.net_profit_label.setText(f"{net_profit:,.2f} {Currency_type}")

        except Exception as e:
            print(f"خطأ في تحميل الإحصائيات: {e}")

    def calculate_completion_percentage(self):
        """حساب نسبة الإنجاز بناءً على المراحل المكتملة"""
        try:
            if not self.project_id:
                return 0

            conn = mysql.connector.connect(
                host=host, user=user_r, password=password_r,
                database="project_manager_V2"
            )
            cursor = conn.cursor()

            # حساب إجمالي المراحل
            cursor.execute("""
                SELECT COUNT(*) FROM المشاريع_مهام_المهندسين
                WHERE معرف_المرحلة IN (
                    SELECT id FROM المشاريع_المراحل WHERE معرف_المشروع = %s
                )
            """, (self.project_id,))
            total_phases = cursor.fetchone()[0]

            if total_phases == 0:
                return 0

            # حساب المراحل المكتملة
            cursor.execute("""
                SELECT COUNT(*) FROM المشاريع_مهام_المهندسين
                WHERE معرف_المرحلة IN (
                    SELECT id FROM المشاريع_المراحل WHERE معرف_المشروع = %s
                ) AND الحالة = 'منتهي'
            """, (self.project_id,))
            completed_phases = cursor.fetchone()[0]

            conn.close()

            return round((completed_phases / total_phases) * 100, 1)

        except Exception as e:
            print(f"خطأ في حساب نسبة الإنجاز: {e}")
            return 0

    def get_total_phases_count(self):
        """جلب إجمالي عدد المراحل"""
        try:
            if not self.project_id:
                return 0

            conn = mysql.connector.connect(
                host=host, user=user_r, password=password_r,
                database="project_manager_V2"
            )
            cursor = conn.cursor()

            cursor.execute("SELECT COUNT(*) FROM المشاريع_المراحل WHERE معرف_المشروع = %s", (self.project_id,))
            result = cursor.fetchone()

            conn.close()

            return result[0] if result else 0

        except Exception as e:
            print(f"خطأ في جلب عدد المراحل: {e}")
            return 0

    def get_total_engineers_amount(self):
        """جلب إجمالي مبالغ المهندسين"""
        try:
            if not self.project_id:
                return 0

            conn = mysql.connector.connect(
                host=host, user=user_r, password=password_r,
                database="project_manager_V2"
            )
            cursor = conn.cursor()

            cursor.execute("""
                SELECT SUM(مبلغ_المهندس) FROM المشاريع_مهام_المهندسين
                WHERE معرف_المرحلة IN (
                    SELECT id FROM المشاريع_المراحل WHERE معرف_المشروع = %s
                )
            """, (self.project_id,))
            result = cursor.fetchone()

            conn.close()

            return result[0] if result and result[0] else 0

        except Exception as e:
            print(f"خطأ في جلب مبالغ المهندسين: {e}")
            return 0

    def load_all_tables_data(self):
        """تحميل بيانات جميع الجداول"""
        try:
            self.load_phases_data()
            self.load_engineers_tasks_data()
            self.load_timeline_data()

            # تحميل بيانات الملفات والمرفقات (لجميع أنواع المشاريع)
            self.load_attachments_data()

            if self.project_type == "مقاولات":
                self.load_expenses_data()
                self.load_custody_data()
                self.load_custody_payments_data()
                self.load_contractors_data()
                self.load_workers_data()
                self.load_suppliers_data()

        except Exception as e:
            print(f"خطأ في تحميل بيانات الجداول: {e}")

    def load_phases_data(self):
        """تحميل بيانات المراحل"""
        try:
            if not self.project_id:
                return

            conn = mysql.connector.connect(
                host=host, user=user_r, password=password_r,
                database="project_manager_V2"
            )
            cursor = conn.cursor()

            cursor.execute("""
                SELECT id, اسم_المرحلة, وصف_المرحلة, الوحدة, الكمية, السعر, الإجمالي, ملاحظات, حالة_المبلغ
                FROM المشاريع_المراحل
                WHERE معرف_المشروع = %s
                ORDER BY id
            """, (self.project_id,))

            rows = cursor.fetchall()
            self.phases_table.setRowCount(len(rows))

            for row_idx, row_data in enumerate(rows):
                # إضافة باقي البيانات مع تعديل الفهارس
                for col_idx, data in enumerate(row_data):
                    if col_idx == 0:  # عمود ID (مخفي)
                        item = QTableWidgetItem(str(data) if data is not None else "")
                        self.phases_table.setItem(row_idx, 0, item)
                    else:  # باقي الأعمدة مع إزاحة بسبب عمود الرقم التلقائي
                        item = QTableWidgetItem(str(data) if data is not None else "")
                        # تطبيق تنسيق الألوان لعمود حالة المبلغ
                        if col_idx == 8:  # عمود حالة المبلغ (الفهرس الأصلي 8)
                            self.apply_amount_status_color(item, str(data) if data is not None else "")
                        item.setTextAlignment(Qt.AlignCenter)

                        self.phases_table.setItem(row_idx, col_idx + 1, item)

                # إضافة أزرار الإجراءات
                #self.add_phase_action_buttons(row_idx)

            # إضافة الأرقام التلقائية
            self.add_auto_numbers_to_table(self.phases_table)

            # تحديث قائمة الفلترة
            self.update_phases_filter_combo()

            conn.close()

        except Exception as e:
            print(f"خطأ في تحميل بيانات المراحل: {e}")

    # def add_phase_action_buttons(self, row):
    #     """إضافة أزرار الإجراءات لصف المرحلة"""
    #     # إنشاء حاوية للأزرار
    #     buttons_widget = QWidget()
    #     buttons_layout = QHBoxLayout(buttons_widget)
    #     buttons_layout.setContentsMargins(5, 2, 5, 2)
    #     buttons_layout.setSpacing(5)

    #     # زر التعديل
    #     edit_btn = QPushButton()
    #     edit_btn.setIcon(qta.icon('fa5s.edit', color='white'))
    #     edit_btn.setToolTip("تعديل المرحلة")
    #     edit_btn.setStyleSheet("""
    #         QPushButton {
    #             background-color: #3498db;
    #             border: none;
    #             border-radius: 3px;
    #             padding: 5px;
    #             min-width: 30px;
    #             max-width: 30px;
    #             min-height: 25px;
    #             max-height: 25px;
    #         }
    #         QPushButton:hover {
    #             background-color: #5dade2;
    #         }
    #     """)
    #     edit_btn.clicked.connect(lambda: self.edit_phase_from_button(row))
    #     buttons_layout.addWidget(edit_btn)

    #     # زر الحذف
    #     delete_btn = QPushButton()
    #     delete_btn.setIcon(qta.icon('fa5s.trash', color='white'))
    #     delete_btn.setToolTip("حذف المرحلة")
    #     delete_btn.setStyleSheet("""
    #         QPushButton {
    #             background-color: #e74c3c;
    #             border: none;
    #             border-radius: 3px;
    #             padding: 5px;
    #             min-width: 30px;
    #             max-width: 30px;
    #             min-height: 25px;
    #             max-height: 25px;
    #         }
    #         QPushButton:hover {
    #             background-color: #ec7063;
    #         }
    #     """)
    #     delete_btn.clicked.connect(lambda: self.delete_phase_from_button(row))
    #     buttons_layout.addWidget(delete_btn)

    #     buttons_layout.addStretch()

    #     # إضافة الحاوية إلى الجدول
    #     self.phases_table.setCellWidget(row, 10, buttons_widget)  # عمود الإجراءات

    def edit_phase_from_button(self, row):
        """تعديل مرحلة من زر الإجراءات"""
        # الحصول على معرف المرحلة من العمود المخفي
        phase_id_item = self.phases_table.item(row, 0)
        if not phase_id_item:
            QMessageBox.warning(self, "خطأ", "لا يمكن الحصول على معرف المرحلة")
            return

        phase_id = int(phase_id_item.text())
        dialog = PhaseDialog(self, project_id=self.project_id, phase_id=phase_id, project_type=self.project_type)
        if dialog.exec() == QDialog.Accepted:
            self.load_phases_data()

    def delete_phase_from_button(self, row):
        """حذف مرحلة من زر الإجراءات"""
        # الحصول على معرف المرحلة واسمها
        phase_id_item = self.phases_table.item(row, 0)
        phase_name_item = self.phases_table.item(row, 2)

        if not phase_id_item or not phase_name_item:
            QMessageBox.warning(self, "خطأ", "لا يمكن الحصول على بيانات المرحلة")
            return

        phase_id = int(phase_id_item.text())
        phase_name = phase_name_item.text()

        # تأكيد الحذف
        reply = QMessageBox.question(
            self, "تأكيد الحذف",
            f"هل أنت متأكد من حذف المرحلة '{phase_name}'؟\n\nهذا الإجراء لا يمكن التراجع عنه.",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                conn = mysql.connector.connect(
                    host=host, user=user_r, password=password_r,
                    database="project_manager_V2"
                )
                cursor = conn.cursor()

                # حذف المرحلة
                cursor.execute("DELETE FROM المشاريع_المراحل WHERE id = %s", (phase_id,))
                conn.commit()
                conn.close()

                QMessageBox.information(self, "نجح", f"تم حذف المرحلة '{phase_name}' بنجاح")
                self.load_phases_data()

            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"فشل في حذف المرحلة: {str(e)}")

    def filter_phases_by_name(self):
        """فلترة المراحل حسب الاسم"""
        filter_text = self.phases_filter_combo.currentText()

        for row in range(self.phases_table.rowCount()):
            if filter_text == "جميع المراحل":
                self.phases_table.setRowHidden(row, False)
            else:
                phase_name_item = self.phases_table.item(row, 2)  # عمود اسم المرحلة
                if phase_name_item:
                    show_row = filter_text in phase_name_item.text()
                    self.phases_table.setRowHidden(row, not show_row)
                else:
                    self.phases_table.setRowHidden(row, True)

    def update_phases_filter_combo(self):
        """تحديث قائمة فلترة المراحل"""
        try:
            if not hasattr(self, 'phases_filter_combo'):
                return

            # حفظ الاختيار الحالي
            current_selection = self.phases_filter_combo.currentText()

            # مسح القائمة وإضافة الخيار الافتراضي
            self.phases_filter_combo.clear()
            self.phases_filter_combo.addItem("جميع المراحل")

            # جمع أسماء المراحل الفريدة من الجدول
            phase_names = set()
            for row in range(self.phases_table.rowCount()):
                phase_name_item = self.phases_table.item(row, 2)  # عمود اسم المرحلة
                if phase_name_item and phase_name_item.text().strip():
                    phase_names.add(phase_name_item.text().strip())

            # إضافة أسماء المراحل مرتبة
            for phase_name in sorted(phase_names):
                self.phases_filter_combo.addItem(phase_name)

            # استعادة الاختيار السابق إن أمكن
            index = self.phases_filter_combo.findText(current_selection)
            if index >= 0:
                self.phases_filter_combo.setCurrentIndex(index)

        except Exception as e:
            print(f"خطأ في تحديث قائمة فلترة المراحل: {e}")

    def load_engineers_tasks_data(self):
        """تحميل بيانات مهام المهندسين"""
        try:
            if not self.project_id:
                return

            conn = mysql.connector.connect(
                host=host, user=user_r, password=password_r,
                database="project_manager_V2"
            )
            cursor = conn.cursor()

            cursor.execute("""
                SELECT
                    مم.id,
                    م.اسم_الموظف,
                    CONCAT(مر.اسم_المرحلة,
                           CASE
                               WHEN مر.وصف_المرحلة IS NOT NULL AND مر.وصف_المرحلة != ''
                               THEN CONCAT(' - ', مر.وصف_المرحلة)
                               ELSE ''
                           END) as وصف_المرحلة_الكامل,
                    مم.نسبة_المهندس,
                    مم.مبلغ_المهندس,
                    مم.حالة_مبلغ_المهندس
                FROM المشاريع_مهام_المهندسين مم
                JOIN الموظفين م ON مم.معرف_المهندس = م.id
                JOIN المشاريع_المراحل مر ON مم.معرف_المرحلة = مر.id
                WHERE مر.معرف_المشروع = %s
                ORDER BY مم.id
            """, (self.project_id,))

            rows = cursor.fetchall()
            self.engineers_tasks_table.setRowCount(len(rows))

            for row_idx, row_data in enumerate(rows):
                # إضافة البيانات مع تعديل الفهارس
                for col_idx, data in enumerate(row_data):
                    if col_idx == 0:  # عمود ID (مخفي)
                        item = QTableWidgetItem(str(data) if data is not None else "")
                        self.engineers_tasks_table.setItem(row_idx, 0, item)
                    else:  # باقي الأعمدة مع إزاحة بسبب عمود الرقم التلقائي
                        item = QTableWidgetItem(str(data) if data is not None else "")

                        # محاذاة الأرقام في المنتصف
                        if col_idx in [3, 4]:  # أعمدة الأرقام (النسبة، المبلغ)
                            item.setTextAlignment(Qt.AlignCenter)

                        # تطبيق تنسيق الألوان لعمود حالة المبلغ
                        if col_idx == 5:  # عمود حالة مبلغ المهندس (الفهرس الأصلي 5)
                            self.apply_amount_status_color(item, str(data) if data is not None else "")

                        self.engineers_tasks_table.setItem(row_idx, col_idx + 1, item)

            # إضافة الأرقام التلقائية
            self.add_auto_numbers_to_table(self.engineers_tasks_table)

            conn.close()

        except Exception as e:
            print(f"خطأ في تحميل بيانات مهام المهندسين: {e}")

    def load_timeline_data(self):
        """تحميل بيانات الجدول الزمني"""
        try:
            if not self.project_id or not hasattr(self, 'timeline_table'):
                return

            conn = mysql.connector.connect(
                host=host, user=user_r, password=password_r,
                database="project_manager_V2"
            )
            cursor = conn.cursor()

            cursor.execute("""
                SELECT
                    مم.id,
                    CONCAT(م.اسم_الموظف,
                           CASE
                               WHEN م.الوظيفة IS NOT NULL AND م.الوظيفة != ''
                               THEN CONCAT(' - ', م.الوظيفة)
                               ELSE ''
                           END) as المهندس_الكامل,
                    CONCAT(مر.اسم_المرحلة,
                           CASE
                               WHEN مر.وصف_المرحلة IS NOT NULL AND مر.وصف_المرحلة != ''
                               THEN CONCAT(' - ', مر.وصف_المرحلة)
                               ELSE ''
                           END) as وصف_المرحلة_الكامل,
                    مم.تاريخ_البداية,
                    CASE
                        WHEN مم.تاريخ_البداية = مم.تاريخ_النهاية THEN 'غير محدد'
                        ELSE مم.تاريخ_النهاية
                    END as تاريخ_النهاية_معدل,
                    CASE
                        WHEN مم.الحالة = 'قيد التنفيذ' AND مم.تاريخ_النهاية != مم.تاريخ_البداية THEN
                            CASE
                                WHEN DATEDIFF(مم.تاريخ_النهاية, CURDATE()) >= 0
                                THEN CONCAT(DATEDIFF(مم.تاريخ_النهاية, CURDATE()), ' يوم متبقي')
                                ELSE CONCAT(ABS(DATEDIFF(مم.تاريخ_النهاية, CURDATE())), ' يوم تأخير')
                            END
                        ELSE مم.الحالة
                    END as حالة_معدلة
                FROM المشاريع_مهام_المهندسين مم
                JOIN الموظفين م ON مم.معرف_المهندس = م.id
                JOIN المشاريع_المراحل مر ON مم.معرف_المرحلة = مر.id
                WHERE مر.معرف_المشروع = %s
                ORDER BY مم.id
            """, (self.project_id,))

            rows = cursor.fetchall()
            self.timeline_table.setRowCount(len(rows))

            for row_idx, row_data in enumerate(rows):
                for col_idx, data in enumerate(row_data):
                    if col_idx == 0:  # عمود ID (مخفي)
                        item = QTableWidgetItem(str(data) if data is not None else "")
                        self.timeline_table.setItem(row_idx, 0, item)
                    else:  # باقي الأعمدة مع إزاحة بسبب عمود الرقم التلقائي
                        # تنسيق خاص للتواريخ
                        if col_idx in [3, 4] and data:  # أعمدة التواريخ
                            formatted_date = str(data) if data else ""
                            item = QTableWidgetItem(formatted_date)
                        else:
                            item = QTableWidgetItem(str(data) if data is not None else "")

                        # محاذاة التواريخ في المنتصف
                        if col_idx in [3, 4]:  # أعمدة التواريخ
                            item.setTextAlignment(Qt.AlignCenter)

                        # تلوين حالة المهمة المعدلة
                        if col_idx == 5:  # عمود حالة المهمة المعدلة
                            if "منتهي" in str(data):
                                item.setForeground(QColor(46, 204, 113, 50))  # أخضر فاتح
                            elif "قيد التنفيذ" in str(data) or "متبقي" in str(data):
                                item.setForeground(QColor(52, 152, 219, 50))  # أزرق فاتح
                            elif "تأخير" in str(data):
                                item.setForeground(QColor(231, 76, 60, 50))   # أحمر فاتح للتأخير
                            elif "متوقف" in str(data):
                                item.setForeground(QColor(230, 126, 34, 50))  # برتقالي للمتوقف
                            elif "لم يبدأ" in str(data):
                                item.setForeground(QColor(149, 165, 166, 50)) # رمادي فاتح

                        self.timeline_table.setItem(row_idx, col_idx + 1, item)

            # إضافة الأرقام التلقائية
            self.add_auto_numbers_to_table(self.timeline_table)

            conn.close()

        except Exception as e:
            print(f"خطأ في تحميل بيانات الجدول الزمني: {e}")

    def on_tab_changed(self, index):
        """معالج تغيير التاب - تحديث البيانات تلقائياً"""
        try:
            # الحصول على اسم التاب الحالي
            tab_text = self.tab_widget.tabText(index)

            # تحديث البيانات حسب التاب المحدد
            if "معلومات المشروع" in tab_text:
                self.load_project_info()
            elif "مراحل المشروع" in tab_text:
                self.load_phases_data()
            elif "مهام المهندسين" in tab_text:
                self.load_engineers_tasks_data()
            elif "الجدول الزمني" in tab_text:
                self.load_timeline_data()
            elif "الملفات والمرفقات" in tab_text:
                self.load_attachments_data()
            elif "المصروفات" in tab_text:
                self.load_expenses_data()
            elif "العهد المالية" in tab_text:
                self.load_custody_data()
            elif "دفعات العهد" in tab_text:
                self.load_custody_payments_data()
            elif "المقاولين" in tab_text:
                self.load_contractors_data()
            elif "العمال" in tab_text:
                self.load_workers_data()
            elif "الموردين" in tab_text:
                self.load_suppliers_data()

            # تمديد أعمدة الجداول بعرض النافذة
            self.extend_table_columns()

        except Exception as e:
            print(f"خطأ في تحديث بيانات التاب: {e}")

    def extend_table_columns(self):
        """تمديد أعمدة الجداول بعرض النافذة"""
        try:
            # قائمة الجداول المراد تمديد أعمدتها
            tables = [
                self.phases_table,
                self.engineers_tasks_table,
                self.timeline_table,
                self.attachments_table
            ]

            # إضافة جداول المقاولات إذا كانت موجودة
            if hasattr(self, 'expenses_table'):
                tables.append(self.expenses_table)
            if hasattr(self, 'custody_table'):
                tables.append(self.custody_table)
            if hasattr(self, 'custody_payments_table'):
                tables.append(self.custody_payments_table)
            if hasattr(self, 'contractors_table'):
                tables.append(self.contractors_table)
            if hasattr(self, 'workers_table'):
                tables.append(self.workers_table)
            if hasattr(self, 'suppliers_table'):
                tables.append(self.suppliers_table)

            for table in tables:
                if table and hasattr(table, 'horizontalHeader'):
                    # تمديد الأعمدة لتملأ عرض الجدول
                    header = table.horizontalHeader()
                    header.setStretchLastSection(True)
                    header.setSectionResizeMode(QHeaderView.Stretch)

        except Exception as e:
            print(f"خطأ في تمديد أعمدة الجداول: {e}")

    def load_expenses_data(self):
        """تحميل بيانات المصروفات"""
        try:
            if not self.project_id or not hasattr(self, 'expenses_table'):
                return
            # تنفيذ أساسي - يمكن تطويره لاحقاً
            self.expenses_table.setRowCount(0)
        except Exception as e:
            print(f"خطأ في تحميل بيانات المصروفات: {e}")

    def load_custody_data(self):
        """تحميل بيانات العهد المالية"""
        try:
            if not self.project_id or not hasattr(self, 'custody_table'):
                return
            # تنفيذ أساسي - يمكن تطويره لاحقاً
            self.custody_table.setRowCount(0)
        except Exception as e:
            print(f"خطأ في تحميل بيانات العهد المالية: {e}")

    def load_custody_payments_data(self):
        """تحميل بيانات دفعات العهد"""
        try:
            if not self.project_id or not hasattr(self, 'custody_payments_table'):
                return
            # تنفيذ أساسي - يمكن تطويره لاحقاً
            self.custody_payments_table.setRowCount(0)
        except Exception as e:
            print(f"خطأ في تحميل بيانات دفعات العهد: {e}")

    def load_contractors_data(self):
        """تحميل بيانات المقاولين"""
        try:
            if not self.project_id or not hasattr(self, 'contractors_table'):
                return
            # تنفيذ أساسي - يمكن تطويره لاحقاً
            self.contractors_table.setRowCount(0)
        except Exception as e:
            print(f"خطأ في تحميل بيانات المقاولين: {e}")

    def load_workers_data(self):
        """تحميل بيانات العمال"""
        try:
            if not self.project_id or not hasattr(self, 'workers_table'):
                return
            # تنفيذ أساسي - يمكن تطويره لاحقاً
            self.workers_table.setRowCount(0)
        except Exception as e:
            print(f"خطأ في تحميل بيانات العمال: {e}")

    def load_suppliers_data(self):
        """تحميل بيانات الموردين"""
        try:
            if not self.project_id or not hasattr(self, 'suppliers_table'):
                return
            # تنفيذ أساسي - يمكن تطويره لاحقاً
            self.suppliers_table.setRowCount(0)
        except Exception as e:
            print(f"خطأ في تحميل بيانات الموردين: {e}")

    def load_attachments_data(self):
        """تحميل بيانات الملفات والمرفقات"""
        try:
            import os

            if not self.project_id:
                return

            # مسار مجلد المرفقات
            attachments_dir = os.path.join(os.getcwd(), "attachments", f"project_{self.project_id}")

            if not os.path.exists(attachments_dir):
                return

            # مسح الجدول
            self.attachments_table.setRowCount(0)

            # قراءة الملفات من المجلد
            for file_name in os.listdir(attachments_dir):
                file_path = os.path.join(attachments_dir, file_name)

                if os.path.isfile(file_path):
                    # الحصول على معلومات الملف
                    file_size = os.path.getsize(file_path)
                    file_extension = os.path.splitext(file_name)[1].lower()
                    file_type = self.get_file_type(file_extension)

                    # إضافة الملف إلى الجدول
                    self.add_attachment_to_table(
                        file_name,
                        file_type,
                        f"ملف {file_type}",  # وصف افتراضي
                        file_path,
                        file_size
                    )

        except Exception as e:
            print(f"خطأ في تحميل بيانات الملفات: {e}")
        
    def get_client_name(self):
        """جلب اسم العميل من قاعدة البيانات"""
        try:
            if not self.client_id:
                return "غير محدد"
                
            conn = mysql.connector.connect(
                host=host, user=user_r, password=password_r, 
                database="project_manager_V2"
            )
            cursor = conn.cursor()
            
            cursor.execute("SELECT اسم_العميل FROM العملاء WHERE id = %s", (self.client_id,))
            result = cursor.fetchone()
            
            conn.close()
            
            return result[0] if result else "غير محدد"
            
        except Exception as e:
            print(f"خطأ في جلب اسم العميل: {e}")
            return "غير محدد"
            
    def get_engineer_name(self):
        """جلب اسم المهندس من قاعدة البيانات"""
        try:
            engineer_id = self.project_data.get('معرف_المهندس')
            if not engineer_id:
                return "غير محدد"
                
            conn = mysql.connector.connect(
                host=host, user=user_r, password=password_r, 
                database="project_manager_V2"
            )
            cursor = conn.cursor()
            
            cursor.execute("SELECT اسم_الموظف FROM الموظفين WHERE id = %s", (engineer_id,))
            result = cursor.fetchone()
            
            conn.close()
            
            return result[0] if result else "غير محدد"
            
        except Exception as e:
            print(f"خطأ في جلب اسم المهندس: {e}")
            return "غير محدد"
            
    def create_timing_status_container(self, parent_layout):
        """إنشاء حاوية معلومات التوقيت والحالة"""
        group = QGroupBox("معلومات التوقيت والحالة")
        group.setStyleSheet("""
            QGroupBox {
                font-size: 14px;
                font-weight: bold;
                color: #2c3e50;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)

        layout = QGridLayout(group)
        layout.setSpacing(15)

        # تاريخ الاستلام
        layout.addWidget(QLabel("تاريخ الاستلام:"), 0, 0)
        self.start_date_label = QLabel()
        self.start_date_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #2c3e50;")
        layout.addWidget(self.start_date_label, 0, 1)

        # تاريخ التسليم المتوقع
        layout.addWidget(QLabel("تاريخ التسليم المتوقع:"), 1, 0)
        self.end_date_label = QLabel()
        self.end_date_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #2c3e50;")
        layout.addWidget(self.end_date_label, 1, 1)

        # # الوقت المتبقي
        # layout.addWidget(QLabel("الوقت المتبقي:"), 2, 0)
        # self.remaining_time_label = QLabel()
        # self.remaining_time_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #f39c12;")
        # layout.addWidget(self.remaining_time_label, 2, 1)

        # # نسبة الإنجاز
        # layout.addWidget(QLabel("نسبة الإنجاز:"), 0, 2)
        # self.completion_percentage_label = QLabel()
        # self.completion_percentage_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #27ae60;")
        # layout.addWidget(self.completion_percentage_label, 0, 3)

        # حالة المشروع الحالية
        layout.addWidget(QLabel("حالة المشروع:"), 2, 0)
        self.project_status_label = QLabel()
        self.project_status_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #3498db;")
        layout.addWidget(self.project_status_label, 2, 1)

        # زر تحرير حالة المشروع
        edit_status_btn = QPushButton("تحرير حالة المشروع")
        edit_status_btn.setIcon(qta.icon('fa5s.edit', color='white'))
        edit_status_btn.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
        """)
        edit_status_btn.clicked.connect(self.edit_project_status)
        layout.addWidget(edit_status_btn, 3, 0, 2, 2)

        parent_layout.addWidget(group)

    def create_description_container(self, parent_layout):
        """إنشاء حاوية الوصف والملاحظات"""
        group = QGroupBox("الوصف والملاحظات")
        group.setStyleSheet("""
            QGroupBox {
                font-size: 14px;
                font-weight: bold;
                color: #2c3e50;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)

        layout = QHBoxLayout(group)
        #layout.setSpacing(5)

        # وصف المشروع
        desc_layout = QVBoxLayout()
        desc_layout.addWidget(QLabel("وصف المشروع:"))
        self.project_description = QTextEdit()
        #self.project_description.setMaximumHeight(100)
        self.project_description.setStyleSheet("""
            QTextEdit {
                border: 1px solid #bdc3c7;
                border-radius: 5px;
                padding: 8px;
                font-size: 14px;
            }
        """)
        desc_layout.addWidget(self.project_description)
        layout.addLayout(desc_layout)

        # ملاحظات
        notes_layout = QVBoxLayout()
        notes_layout.addWidget(QLabel("الملاحظات:"))
        self.project_notes = QTextEdit()
        #self.project_notes.setMaximumHeight(100)
        self.project_notes.setStyleSheet("""
            QTextEdit {
                border: 1px solid #bdc3c7;
                border-radius: 5px;
                padding: 8px;
                font-size: 14px;
            }
        """)
        notes_layout.addWidget(self.project_notes)
        layout.addLayout(notes_layout)

        parent_layout.addWidget(group)

    def create_statistics_container(self, parent_layout):
        """إنشاء حاوية الإحصائيات المالية"""
        group = QGroupBox("الإحصائيات المالية")
        group.setStyleSheet("""
            QGroupBox {
                font-size: 14px;
                font-weight: bold;
                color: #2c3e50;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)

        layout = QGridLayout(group)
        layout.setSpacing(15)

        # إجمالي عدد المراحل
        layout.addWidget(QLabel("إجمالي عدد المراحل:"), 0, 0)
        self.total_phases_label = QLabel("0")
        self.total_phases_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #2c3e50;")
        layout.addWidget(self.total_phases_label, 0, 1)

        # إجمالي حسابات المهندسين
        layout.addWidget(QLabel("إجمالي حسابات المهندسين:"), 1, 0)
        self.total_engineers_amount_label = QLabel("0.00")
        self.total_engineers_amount_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #3498db;")
        layout.addWidget(self.total_engineers_amount_label, 1, 1)

        # صافي ربح الشركة
        layout.addWidget(QLabel("صافي ربح الشركة:"), 2, 0)
        self.net_profit_label = QLabel("0.00")
        self.net_profit_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #27ae60;")
        layout.addWidget(self.net_profit_label, 2, 1)

        parent_layout.addWidget(group)

    def create_project_phases_tab(self):
        """إنشاء تاب مراحل المشروع"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # تخطيط أفقي للأزرار وشريط البحث
        top_layout = QHBoxLayout()

        # أزرار العمليات (في الجانب الأيمن)
        buttons_layout = QHBoxLayout()

        add_phase_btn = QPushButton("إضافة")
        add_phase_btn.setIcon(qta.icon('fa5s.plus'))
        add_phase_btn.clicked.connect(self.add_phase)
        add_phase_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #2ecc71;
            }
        """)
        buttons_layout.addWidget(add_phase_btn)

        edit_phase_btn = QPushButton("تعديل")
        edit_phase_btn.setIcon(qta.icon('fa5s.edit'))
        edit_phase_btn.clicked.connect(self.edit_phase)
        edit_phase_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #5dade2;
            }
        """)
        buttons_layout.addWidget(edit_phase_btn)

        delete_phase_btn = QPushButton("حذف")
        delete_phase_btn.setIcon(qta.icon('fa5s.trash'))
        delete_phase_btn.clicked.connect(self.delete_phase)
        delete_phase_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #ec7063;
            }
        """)
        buttons_layout.addWidget(delete_phase_btn)

        # إضافة الأزرار الجديدة لتاب مراحل المشروع
        insert_amount_btn = QPushButton("إدراج المبلغ")
        insert_amount_btn.setIcon(qta.icon('fa5s.money-bill'))
        insert_amount_btn.clicked.connect(self.insert_phase_amount)
        insert_amount_btn.setStyleSheet("""
            QPushButton {
                background-color: #16a085;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #138d75;
            }
        """)
        buttons_layout.addWidget(insert_amount_btn)

        insert_all_amounts_btn = QPushButton("إدراج جميع المبالغ")
        insert_all_amounts_btn.setIcon(qta.icon('fa5s.coins'))
        insert_all_amounts_btn.clicked.connect(self.insert_all_phase_amounts)
        insert_all_amounts_btn.setStyleSheet("""
            QPushButton {
                background-color: #8e44ad;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #9b59b6;
            }
        """)
        buttons_layout.addWidget(insert_all_amounts_btn)

        top_layout.addLayout(buttons_layout)

        # مساحة فارغة
        top_layout.addStretch()

        # ComboBox للفلترة حسب اسم المرحلة
        filter_layout = QHBoxLayout()
        filter_layout.addWidget(QLabel("فلترة حسب المرحلة:"))
        self.phases_filter_combo = QComboBox()
        self.phases_filter_combo.addItem("جميع المراحل")
        self.phases_filter_combo.currentTextChanged.connect(self.filter_phases_by_name)
        filter_layout.addWidget(self.phases_filter_combo)

        # شريط البحث (في الجانب الأيسر)
        search_layout = QHBoxLayout()
        search_layout.addWidget(QLabel("البحث:"))
        self.phases_search = QLineEdit()
        self.phases_search.setPlaceholderText("ابحث في مراحل المشروع...")
        self.phases_search.textChanged.connect(self.filter_phases)
        search_layout.addWidget(self.phases_search)

        filter_layout.addLayout(search_layout)
        top_layout.addLayout(filter_layout)
        layout.addLayout(top_layout)

        # جدول المراحل
        self.phases_table = QTableWidget()
        self.setup_phases_table()
        layout.addWidget(self.phases_table)

        self.tab_widget.addTab(tab, qta.icon('fa5s.tasks', color='#9b59b6'), "مراحل المشروع")

    def setup_phases_table(self):
        """إعداد جدول المراحل"""
        headers = ["ID", "الرقم", "اسم المرحلة", "وصف المرحلة", "الوحدة", "الكمية", "السعر", "الإجمالي", "ملاحظات", "حالة المبلغ"]
        self.phases_table.setColumnCount(len(headers))
        self.phases_table.setHorizontalHeaderLabels(headers)
        self.phases_table.hideColumn(0)  # إخفاء عمود ID

        # تطبيق إعدادات الجدول
        table_setting(self.phases_table)

        # تعيين عرض عمود الإجراءات
        self.phases_table.setColumnWidth(10, 150)  # عمود الإجراءات

        # إضافة وظيفة النقر المزدوج لفتح حوار التعديل
        self.phases_table.itemDoubleClicked.connect(self.on_phases_table_double_click)

    def add_new_payment(self):
        """إضافة دفعة جديدة"""
        QMessageBox.information(self, "إضافة دفعة", "سيتم فتح نافذة إضافة دفعة جديدة")

    def view_payments(self):
        """عرض الدفعات"""
        QMessageBox.information(self, "عرض الدفعات", "سيتم فتح نافذة عرض الدفعات")

    def edit_project_status(self):
        """تحرير حالة المشروع"""
        QMessageBox.information(self, "تحرير الحالة", "سيتم فتح نافذة تحرير حالة المشروع")

    def filter_phases(self):
        """تصفية المراحل"""
        search_text = self.phases_search.text().lower()
        for row in range(self.phases_table.rowCount()):
            show_row = False
            for col in range(self.phases_table.columnCount()):
                item = self.phases_table.item(row, col)
                if item and search_text in item.text().lower():
                    show_row = True
                    break
            self.phases_table.setRowHidden(row, not show_row)

    def add_phase(self):
        """إضافة مرحلة جديدة"""
        dialog = PhaseDialog(self, project_id=self.project_id, project_type=self.project_type)
        if dialog.exec() == QDialog.Accepted:
            self.load_phases_data()

    def edit_phase(self):
        """تعديل مرحلة"""
        current_row = self.phases_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد مرحلة للتعديل")
            return

        # الحصول على معرف المرحلة من العمود المخفي
        phase_id_item = self.phases_table.item(current_row, 0)
        if not phase_id_item:
            QMessageBox.warning(self, "خطأ", "لا يمكن الحصول على معرف المرحلة")
            return

        phase_id = int(phase_id_item.text())
        dialog = PhaseDialog(self, project_id=self.project_id, phase_id=phase_id, project_type=self.project_type)
        if dialog.exec() == QDialog.Accepted:
            self.load_phases_data()

    def delete_phase(self):
        """حذف مرحلة"""
        current_row = self.phases_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد مرحلة للحذف")
            return

        # الحصول على معرف المرحلة واسمها
        phase_id_item = self.phases_table.item(current_row, 0)
        phase_name_item = self.phases_table.item(current_row, 2)

        if not phase_id_item or not phase_name_item:
            QMessageBox.warning(self, "خطأ", "لا يمكن الحصول على بيانات المرحلة")
            return

        phase_id = int(phase_id_item.text())
        phase_name = phase_name_item.text()

        # تأكيد الحذف
        reply = QMessageBox.question(
            self, "تأكيد الحذف",
            f"هل أنت متأكد من حذف المرحلة '{phase_name}'؟\n\nهذا الإجراء لا يمكن التراجع عنه.",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                conn = mysql.connector.connect(
                    host=host, user=user_r, password=password_r,
                    database="project_manager_V2"
                )
                cursor = conn.cursor()

                # حذف المرحلة
                cursor.execute("DELETE FROM المشاريع_المراحل WHERE id = %s", (phase_id,))
                conn.commit()
                conn.close()

                QMessageBox.information(self, "نجح", f"تم حذف المرحلة '{phase_name}' بنجاح")
                self.load_phases_data()

            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"فشل في حذف المرحلة: {str(e)}")

    def create_engineers_tasks_tab(self):
        """إنشاء تاب مهام المهندسين"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # تخطيط أفقي للأزرار وشريط البحث
        top_layout = QHBoxLayout()

        # أزرار العمليات (في الجانب الأيمن)
        buttons_layout = QHBoxLayout()

        add_task_btn = QPushButton("إضافة")
        add_task_btn.setIcon(qta.icon('fa5s.plus'))
        add_task_btn.clicked.connect(self.add_engineer_task)
        add_task_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #2ecc71;
            }
        """)
        buttons_layout.addWidget(add_task_btn)

        edit_task_btn = QPushButton("تعديل")
        edit_task_btn.setIcon(qta.icon('fa5s.edit'))
        edit_task_btn.clicked.connect(self.edit_engineer_task)
        edit_task_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #5dade2;
            }
        """)
        buttons_layout.addWidget(edit_task_btn)

        delete_task_btn = QPushButton("حذف")
        delete_task_btn.setIcon(qta.icon('fa5s.trash'))
        delete_task_btn.clicked.connect(self.delete_engineer_task)
        delete_task_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #ec7063;
            }
        """)
        buttons_layout.addWidget(delete_task_btn)

        # إضافة الأزرار الجديدة لتاب مهام المهندسين
        insert_balance_btn = QPushButton("إدراج الرصيد للمهندس")
        insert_balance_btn.setIcon(qta.icon('fa5s.user-plus'))
        insert_balance_btn.clicked.connect(self.insert_engineer_balance)
        insert_balance_btn.setStyleSheet("""
            QPushButton {
                background-color: #16a085;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
                min-width: 140px;
            }
            QPushButton:hover {
                background-color: #138d75;
            }
        """)
        buttons_layout.addWidget(insert_balance_btn)

        insert_all_balances_btn = QPushButton("إدراج جميع الأرصدة")
        insert_all_balances_btn.setIcon(qta.icon('fa5s.users-cog'))
        insert_all_balances_btn.clicked.connect(self.insert_all_engineer_balances)
        insert_all_balances_btn.setStyleSheet("""
            QPushButton {
                background-color: #8e44ad;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #9b59b6;
            }
        """)
        buttons_layout.addWidget(insert_all_balances_btn)

        top_layout.addLayout(buttons_layout)

        # مساحة فارغة
        top_layout.addStretch()

        # ComboBox للفلترة حسب اسم المهندس
        filter_layout = QHBoxLayout()
        filter_layout.addWidget(QLabel("فلترة حسب المهندس:"))
        self.engineers_filter_combo = QComboBox()
        self.engineers_filter_combo.addItem("جميع المهندسين")
        self.engineers_filter_combo.currentTextChanged.connect(self.filter_engineers_by_name)
        filter_layout.addWidget(self.engineers_filter_combo)

        # شريط البحث (في الجانب الأيسر)
        search_layout = QHBoxLayout()
        search_layout.addWidget(QLabel("البحث:"))
        self.engineers_search = QLineEdit()
        self.engineers_search.setPlaceholderText("ابحث في مهام المهندسين...")
        self.engineers_search.textChanged.connect(self.filter_engineers_tasks)
        search_layout.addWidget(self.engineers_search)

        filter_layout.addLayout(search_layout)
        top_layout.addLayout(filter_layout)
        layout.addLayout(top_layout)

        # جدول مهام المهندسين
        self.engineers_tasks_table = QTableWidget()
        self.setup_engineers_tasks_table()
        layout.addWidget(self.engineers_tasks_table)

        self.tab_widget.addTab(tab, qta.icon('fa5s.users', color='#16a085'), "مهام المهندسين")

    def setup_engineers_tasks_table(self):
        """إعداد جدول مهام المهندسين"""
        headers = ["ID", "الرقم", "المهندس", "وصف المرحلة", "% النسبة", "مبلغ المهندس", "حالة المبلغ"]
        self.engineers_tasks_table.setColumnCount(len(headers))
        self.engineers_tasks_table.setHorizontalHeaderLabels(headers)
        self.engineers_tasks_table.hideColumn(0)  # إخفاء عمود ID

        # تطبيق إعدادات الجدول
        table_setting(self.engineers_tasks_table)

        # إضافة وظيفة النقر المزدوج لفتح حوار التعديل
        self.engineers_tasks_table.itemDoubleClicked.connect(self.on_engineers_tasks_table_double_click)

    def create_timeline_tab(self):
        """إنشاء تاب الجدول الزمني"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # تخطيط أفقي للأزرار وشريط البحث
        top_layout = QHBoxLayout()

        # أزرار العمليات (في الجانب الأيمن)
        buttons_layout = QHBoxLayout()

        add_timeline_btn = QPushButton("إضافة")
        add_timeline_btn.setIcon(qta.icon('fa5s.plus'))
        add_timeline_btn.clicked.connect(self.add_timeline_entry)
        add_timeline_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #2ecc71;
            }
        """)
        buttons_layout.addWidget(add_timeline_btn)

        edit_timeline_btn = QPushButton("تعديل")
        edit_timeline_btn.setIcon(qta.icon('fa5s.edit'))
        edit_timeline_btn.clicked.connect(self.edit_timeline_entry)
        edit_timeline_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #5dade2;
            }
        """)
        buttons_layout.addWidget(edit_timeline_btn)

        delete_timeline_btn = QPushButton("حذف")
        delete_timeline_btn.setIcon(qta.icon('fa5s.trash'))
        delete_timeline_btn.clicked.connect(self.delete_timeline_entry)
        delete_timeline_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #ec7063;
            }
        """)
        buttons_layout.addWidget(delete_timeline_btn)

        # إضافة زر الحالة لتاب الجدول الزمني
        status_btn = QPushButton("الحالة")
        status_btn.setIcon(qta.icon('fa5s.tasks'))
        status_btn.clicked.connect(self.manage_timeline_status)
        status_btn.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
        """)
        buttons_layout.addWidget(status_btn)

        top_layout.addLayout(buttons_layout)

        # مساحة فارغة
        top_layout.addStretch()

        # ComboBox للفلترة حسب الحالة
        filter_layout = QHBoxLayout()
        filter_layout.addWidget(QLabel("فلترة حسب الحالة:"))
        self.timeline_status_filter_combo = QComboBox()
        self.timeline_status_filter_combo.addItem("جميع الحالات")
        self.timeline_status_filter_combo.addItem("لم يبدأ")
        self.timeline_status_filter_combo.addItem("قيد التنفيذ")
        self.timeline_status_filter_combo.addItem("منتهي")
        self.timeline_status_filter_combo.addItem("متوقف")
        self.timeline_status_filter_combo.currentTextChanged.connect(self.filter_timeline_by_status)
        filter_layout.addWidget(self.timeline_status_filter_combo)

        # شريط البحث (في الجانب الأيسر)
        search_layout = QHBoxLayout()
        search_layout.addWidget(QLabel("البحث:"))
        self.timeline_search = QLineEdit()
        self.timeline_search.setPlaceholderText("ابحث في الجدول الزمني...")
        self.timeline_search.textChanged.connect(self.filter_timeline)
        search_layout.addWidget(self.timeline_search)

        filter_layout.addLayout(search_layout)
        top_layout.addLayout(filter_layout)
        layout.addLayout(top_layout)

        # جدول الجدول الزمني
        self.timeline_table = QTableWidget()
        self.setup_timeline_table()
        layout.addWidget(self.timeline_table)

        self.tab_widget.addTab(tab, qta.icon('fa5s.calendar', color='#f39c12'), "الجدول الزمني")

    def setup_timeline_table(self):
        """إعداد جدول الجدول الزمني"""
        headers = ["ID", "الرقم", "المهندس", "وصف المرحلة", "تاريخ البدء", "تاريخ الانتهاء", "حالة المهمة"]
        self.timeline_table.setColumnCount(len(headers))
        self.timeline_table.setHorizontalHeaderLabels(headers)
        self.timeline_table.hideColumn(0)  # إخفاء عمود ID

        # تطبيق إعدادات الجدول
        table_setting(self.timeline_table)

        # إضافة وظيفة النقر المزدوج لفتح حوار التعديل
        self.timeline_table.itemDoubleClicked.connect(self.on_timeline_table_double_click)

    def create_reports_tab(self):
        """إنشاء تاب التقارير الشاملة"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # عنوان التقارير
        title_label = QLabel("التقارير الشاملة")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 10px;
                background-color: #ecf0f1;
                border-radius: 5px;
                margin-bottom: 10px;
            }
        """)
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)

        # أزرار التقارير
        reports_layout = QGridLayout()

        # تقرير المراحل
        phases_report_btn = QPushButton("تقرير المراحل")
        phases_report_btn.setIcon(qta.icon('fa5s.file-alt'))
        phases_report_btn.clicked.connect(self.generate_phases_report)
        reports_layout.addWidget(phases_report_btn, 0, 0)

        # تقرير المهندسين
        engineers_report_btn = QPushButton("تقرير المهندسين")
        engineers_report_btn.setIcon(qta.icon('fa5s.users'))
        engineers_report_btn.clicked.connect(self.generate_engineers_report)
        reports_layout.addWidget(engineers_report_btn, 0, 1)

        # تقرير الجدول الزمني
        timeline_report_btn = QPushButton("تقرير الجدول الزمني")
        timeline_report_btn.setIcon(qta.icon('fa5s.calendar'))
        timeline_report_btn.clicked.connect(self.generate_timeline_report)
        reports_layout.addWidget(timeline_report_btn, 1, 0)

        # تقرير مالي شامل
        financial_report_btn = QPushButton("تقرير مالي شامل")
        financial_report_btn.setIcon(qta.icon('fa5s.chart-line'))
        financial_report_btn.clicked.connect(self.generate_financial_report)
        reports_layout.addWidget(financial_report_btn, 1, 1)

        layout.addLayout(reports_layout)
        layout.addStretch()

        self.tab_widget.addTab(tab, qta.icon('fa5s.chart-bar', color='#e74c3c'), "تقارير شاملة")

    def create_attachments_tab(self):
        """إنشاء تاب الملفات والمرفقات"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # تخطيط أفقي للأزرار وشريط البحث
        top_layout = QHBoxLayout()

        # أزرار العمليات (في الجانب الأيمن)
        buttons_layout = QHBoxLayout()

        add_file_btn = QPushButton("إضافة")
        add_file_btn.setIcon(qta.icon('fa5s.plus'))
        add_file_btn.clicked.connect(self.add_attachment)
        add_file_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #2ecc71;
            }
        """)
        buttons_layout.addWidget(add_file_btn)

        view_file_btn = QPushButton("عرض")
        view_file_btn.setIcon(qta.icon('fa5s.eye'))
        view_file_btn.clicked.connect(self.view_attachment)
        view_file_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #5dade2;
            }
        """)
        buttons_layout.addWidget(view_file_btn)

        download_file_btn = QPushButton("تحميل")
        download_file_btn.setIcon(qta.icon('fa5s.download'))
        download_file_btn.clicked.connect(self.download_attachment)
        download_file_btn.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
        """)
        buttons_layout.addWidget(download_file_btn)

        delete_file_btn = QPushButton("حذف")
        delete_file_btn.setIcon(qta.icon('fa5s.trash'))
        delete_file_btn.clicked.connect(self.delete_attachment)
        delete_file_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #ec7063;
            }
        """)
        buttons_layout.addWidget(delete_file_btn)

        top_layout.addLayout(buttons_layout)

        # مساحة فارغة
        top_layout.addStretch()

        # شريط البحث (في الجانب الأيسر)
        search_layout = QHBoxLayout()
        search_layout.addWidget(QLabel("البحث:"))
        self.attachments_search = QLineEdit()
        self.attachments_search.setPlaceholderText("ابحث في الملفات والمرفقات...")
        self.attachments_search.textChanged.connect(self.filter_attachments)
        search_layout.addWidget(self.attachments_search)

        top_layout.addLayout(search_layout)
        layout.addLayout(top_layout)

        # جدول الملفات والمرفقات
        self.attachments_table = QTableWidget()
        self.setup_attachments_table()
        layout.addWidget(self.attachments_table)

        self.tab_widget.addTab(tab, qta.icon('fa5s.paperclip', color='#95a5a6'), "الملفات والمرفقات")

    def setup_attachments_table(self):
        """إعداد جدول الملفات والمرفقات"""
        headers = ["ID", "الرقم", "اسم الملف", "نوع الملف", "الوصف", "المسار", "تاريخ الإضافة", "حجم الملف"]
        self.attachments_table.setColumnCount(len(headers))
        self.attachments_table.setHorizontalHeaderLabels(headers)
        self.attachments_table.hideColumn(0)  # إخفاء عمود ID
        self.attachments_table.hideColumn(5)  # إخفاء عمود المسار للأمان (تم تعديل الفهرس)

        # تطبيق إعدادات الجدول
        table_setting(self.attachments_table)

        # إضافة وظيفة النقر المزدوج لفتح حوار التعديل
        self.attachments_table.itemDoubleClicked.connect(self.on_attachments_table_double_click)

        # تعديل عرض الأعمدة
        self.attachments_table.setColumnWidth(1, 200)  # اسم الملف
        self.attachments_table.setColumnWidth(2, 120)  # نوع الملف
        self.attachments_table.setColumnWidth(3, 300)  # الوصف
        self.attachments_table.setColumnWidth(5, 150)  # تاريخ الإضافة
        self.attachments_table.setColumnWidth(6, 100)  # حجم الملف

    # دوال معالجة الأحداث للتابات الجديدة
    def filter_engineers_tasks(self):
        """تصفية مهام المهندسين"""
        search_text = self.engineers_search.text().lower()
        for row in range(self.engineers_tasks_table.rowCount()):
            show_row = False
            for col in range(self.engineers_tasks_table.columnCount()):
                item = self.engineers_tasks_table.item(row, col)
                if item and search_text in item.text().lower():
                    show_row = True
                    break
            self.engineers_tasks_table.setRowHidden(row, not show_row)

    def filter_timeline(self):
        """تصفية الجدول الزمني"""
        search_text = self.timeline_search.text().lower()
        for row in range(self.timeline_table.rowCount()):
            show_row = False
            for col in range(self.timeline_table.columnCount()):
                item = self.timeline_table.item(row, col)
                if item and search_text in item.text().lower():
                    show_row = True
                    break
            self.timeline_table.setRowHidden(row, not show_row)

    def add_engineer_task(self):
        """إضافة مهمة مهندس"""
        if not self.project_id:
            QMessageBox.warning(self, "تحذير", "لا يوجد مشروع محدد")
            return

        dialog = EngineerTaskDialog(self, project_id=self.project_id)
        if dialog.exec() == QDialog.Accepted:
            self.load_engineers_tasks_data()
            # تحديث الإحصائيات
            self.load_statistics()

    def edit_engineer_task(self):
        """تعديل مهمة مهندس"""
        current_row = self.engineers_tasks_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد مهمة للتعديل")
            return

        # الحصول على معرف المهمة من العمود المخفي
        task_id_item = self.engineers_tasks_table.item(current_row, 0)
        if not task_id_item:
            QMessageBox.warning(self, "خطأ", "لا يمكن الحصول على معرف المهمة")
            return

        task_id = int(task_id_item.text())
        dialog = EngineerTaskDialog(self, project_id=self.project_id, task_id=task_id)
        if dialog.exec() == QDialog.Accepted:
            self.load_engineers_tasks_data()
            # تحديث الإحصائيات
            self.load_statistics()

    def delete_engineer_task(self):
        """حذف مهمة مهندس"""
        current_row = self.engineers_tasks_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد مهمة للحذف")
            return

        # الحصول على معرف المهمة واسم المهندس
        task_id_item = self.engineers_tasks_table.item(current_row, 0)
        engineer_name_item = self.engineers_tasks_table.item(current_row, 2)

        if not task_id_item or not engineer_name_item:
            QMessageBox.warning(self, "خطأ", "لا يمكن الحصول على بيانات المهمة")
            return

        task_id = int(task_id_item.text())
        engineer_name = engineer_name_item.text()

        # تأكيد الحذف
        reply = QMessageBox.question(
            self, "تأكيد الحذف",
            f"هل أنت متأكد من حذف مهمة المهندس '{engineer_name}'؟\n\nهذا الإجراء لا يمكن التراجع عنه.",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                conn = mysql.connector.connect(
                    host=host, user=user_r, password=password_r,
                    database="project_manager_V2"
                )
                cursor = conn.cursor()

                # حذف المهمة
                cursor.execute("DELETE FROM المشاريع_مهام_المهندسين WHERE id = %s", (task_id,))
                conn.commit()
                conn.close()

                QMessageBox.information(self, "نجح", f"تم حذف مهمة المهندس '{engineer_name}' بنجاح")
                self.load_engineers_tasks_data()
                # تحديث الإحصائيات
                self.load_statistics()

            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"فشل في حذف المهمة: {str(e)}")

    def add_timeline_entry(self):
        """إضافة إدخال جدول زمني"""
        if not self.project_id:
            QMessageBox.warning(self, "تحذير", "لا يوجد مشروع محدد")
            return

        dialog = TimelineEntryDialog(self, project_id=self.project_id)
        if dialog.exec() == QDialog.Accepted:
            self.load_timeline_data()

    def edit_timeline_entry(self):
        """تعديل إدخال جدول زمني"""
        current_row = self.timeline_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد جدولة للتعديل")
            return

        # الحصول على معرف الجدولة من العمود المخفي
        entry_id_item = self.timeline_table.item(current_row, 0)
        if not entry_id_item:
            QMessageBox.warning(self, "خطأ", "لا يمكن الحصول على معرف الجدولة")
            return

        entry_id = int(entry_id_item.text())
        dialog = TimelineEntryDialog(self, project_id=self.project_id, entry_id=entry_id)
        if dialog.exec() == QDialog.Accepted:
            self.load_timeline_data()

    def delete_timeline_entry(self):
        """حذف إدخال جدول زمني"""
        current_row = self.timeline_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد جدولة للحذف")
            return

        # الحصول على معرف الجدولة واسم المهندس
        entry_id_item = self.timeline_table.item(current_row, 0)
        engineer_name_item = self.timeline_table.item(current_row, 2)

        if not entry_id_item or not engineer_name_item:
            QMessageBox.warning(self, "خطأ", "لا يمكن الحصول على بيانات الجدولة")
            return

        entry_id = int(entry_id_item.text())
        engineer_name = engineer_name_item.text()

        # تأكيد الحذف
        reply = QMessageBox.question(
            self, "تأكيد الحذف",
            f"هل أنت متأكد من حذف الجدولة الزمنية للمهندس '{engineer_name}'؟\n\nهذا الإجراء لا يمكن التراجع عنه.",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                conn = mysql.connector.connect(
                    host=host, user=user_r, password=password_r,
                    database="project_manager_V2"
                )
                cursor = conn.cursor()

                # حذف الجدولة
                cursor.execute("DELETE FROM المشاريع_مهام_المهندسين WHERE id = %s", (entry_id,))
                conn.commit()
                conn.close()

                QMessageBox.information(self, "نجح", f"تم حذف الجدولة الزمنية للمهندس '{engineer_name}' بنجاح")
                self.load_timeline_data()

            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"فشل في حذف الجدولة: {str(e)}")

    # ==================== دوال النقر المزدوج للجداول ====================

    def on_phases_table_double_click(self, item):
        """معالج النقر المزدوج على جدول المراحل"""
        if item is not None:
            self.edit_phase()

    def on_engineers_tasks_table_double_click(self, item):
        """معالج النقر المزدوج على جدول مهام المهندسين"""
        if item is not None:
            self.edit_engineer_task()

    def on_timeline_table_double_click(self, item):
        """معالج النقر المزدوج على جدول الجدول الزمني"""
        if item is not None:
            self.edit_timeline_entry()

    def on_expenses_table_double_click(self, item):
        """معالج النقر المزدوج على جدول المصروفات"""
        if item is not None:
            self.edit_expense()

    def on_custody_table_double_click(self, item):
        """معالج النقر المزدوج على جدول العهد المالية"""
        if item is not None:
            self.edit_custody()

    def on_custody_payments_table_double_click(self, item):
        """معالج النقر المزدوج على جدول دفعات العهد"""
        if item is not None:
            self.edit_custody_payment()

    def on_contractors_table_double_click(self, item):
        """معالج النقر المزدوج على جدول المقاولين"""
        if item is not None:
            self.edit_contractor()

    def on_workers_table_double_click(self, item):
        """معالج النقر المزدوج على جدول العمال"""
        if item is not None:
            self.edit_worker()

    def on_suppliers_table_double_click(self, item):
        """معالج النقر المزدوج على جدول الموردين"""
        if item is not None:
            self.edit_supplier()

    def on_attachments_table_double_click(self, item):
        """معالج النقر المزدوج على جدول الملفات والمرفقات"""
        if item is not None:
            self.view_attachment()

    def generate_phases_report(self):
        """إنتاج تقرير المراحل"""
        QMessageBox.information(self, "تقرير المراحل", "سيتم إنتاج تقرير شامل للمراحل")

    def generate_engineers_report(self):
        """إنتاج تقرير المهندسين"""
        QMessageBox.information(self, "تقرير المهندسين", "سيتم إنتاج تقرير شامل للمهندسين")

    def generate_timeline_report(self):
        """إنتاج تقرير الجدول الزمني"""
        QMessageBox.information(self, "تقرير الجدول الزمني", "سيتم إنتاج تقرير شامل للجدول الزمني")

    def generate_financial_report(self):
        """إنتاج تقرير مالي شامل"""
        QMessageBox.information(self, "تقرير مالي", "سيتم إنتاج تقرير مالي شامل للمشروع")

    # ==================== دوال معالجة تاب الملفات والمرفقات ====================

    def filter_attachments(self):
        """تصفية الملفات والمرفقات"""
        search_text = self.attachments_search.text().lower()
        for row in range(self.attachments_table.rowCount()):
            show_row = False
            for col in range(self.attachments_table.columnCount()):
                item = self.attachments_table.item(row, col)
                if item and search_text in item.text().lower():
                    show_row = True
                    break
            self.attachments_table.setRowHidden(row, not show_row)

    def add_attachment(self):
        """إضافة ملف جديد"""
        try:
            from PySide6.QtWidgets import QFileDialog
            import os
            import shutil

            # فتح نافذة اختيار الملف
            file_path, _ = QFileDialog.getOpenFileName(
                self,
                "اختيار ملف للإرفاق",
                "",
                "جميع الملفات (*.*)"
            )

            if file_path:
                # الحصول على معلومات الملف
                file_name = os.path.basename(file_path)
                file_extension = os.path.splitext(file_name)[1].lower()
                file_size = os.path.getsize(file_path)

                # تحديد نوع الملف
                file_type = self.get_file_type(file_extension)

                # طلب وصف للملف
                description, ok = QInputDialog.getText(
                    self,
                    "وصف الملف",
                    "أدخل وصفاً للملف:",
                    text=f"ملف {file_type}"
                )

                if ok:
                    # إنشاء مجلد المرفقات إذا لم يكن موجوداً
                    attachments_dir = os.path.join(os.getcwd(), "attachments", f"project_{self.project_id}")
                    os.makedirs(attachments_dir, exist_ok=True)

                    # نسخ الملف إلى مجلد المرفقات
                    destination_path = os.path.join(attachments_dir, file_name)

                    # التأكد من عدم وجود ملف بنفس الاسم
                    counter = 1
                    original_destination = destination_path
                    while os.path.exists(destination_path):
                        name, ext = os.path.splitext(original_destination)
                        destination_path = f"{name}_{counter}{ext}"
                        counter += 1

                    shutil.copy2(file_path, destination_path)

                    # إضافة الملف إلى الجدول
                    self.add_attachment_to_table(
                        os.path.basename(destination_path),
                        file_type,
                        description,
                        destination_path,
                        file_size
                    )

                    QMessageBox.information(self, "نجح", f"تم إرفاق الملف '{file_name}' بنجاح")

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في إرفاق الملف: {str(e)}")

    def get_file_type(self, extension):
        """تحديد نوع الملف بناءً على الامتداد"""
        image_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff']
        document_extensions = ['.pdf', '.doc', '.docx', '.txt', '.rtf']
        spreadsheet_extensions = ['.xls', '.xlsx', '.csv']
        presentation_extensions = ['.ppt', '.pptx']
        cad_extensions = ['.dwg', '.dxf', '.dwf']

        if extension in image_extensions:
            return "صورة"
        elif extension in document_extensions:
            return "مستند"
        elif extension in spreadsheet_extensions:
            return "جدول بيانات"
        elif extension in presentation_extensions:
            return "عرض تقديمي"
        elif extension in cad_extensions:
            return "ملف CAD"
        else:
            return "ملف عام"

    def add_attachment_to_table(self, file_name, file_type, description, file_path, file_size):
        """إضافة ملف إلى الجدول"""
        from datetime import datetime

        row_count = self.attachments_table.rowCount()
        self.attachments_table.insertRow(row_count)

        # تحويل حجم الملف إلى تنسيق قابل للقراءة
        size_str = self.format_file_size(file_size)

        # إضافة البيانات إلى الجدول
        self.attachments_table.setItem(row_count, 0, QTableWidgetItem(str(row_count + 1)))  # ID مؤقت
        self.attachments_table.setItem(row_count, 1, QTableWidgetItem(file_name))
        self.attachments_table.setItem(row_count, 2, QTableWidgetItem(file_type))
        self.attachments_table.setItem(row_count, 3, QTableWidgetItem(description))
        self.attachments_table.setItem(row_count, 4, QTableWidgetItem(file_path))
        self.attachments_table.setItem(row_count, 5, QTableWidgetItem(datetime.now().strftime("%Y-%m-%d %H:%M")))
        self.attachments_table.setItem(row_count, 6, QTableWidgetItem(size_str))

    def format_file_size(self, size_bytes):
        """تحويل حجم الملف إلى تنسيق قابل للقراءة"""
        if size_bytes == 0:
            return "0 B"

        size_names = ["B", "KB", "MB", "GB"]
        import math
        i = int(math.floor(math.log(size_bytes, 1024)))
        p = math.pow(1024, i)
        s = round(size_bytes / p, 2)
        return f"{s} {size_names[i]}"

    def view_attachment(self):
        """عرض الملف المحدد"""
        current_row = self.attachments_table.currentRow()
        if current_row >= 0:
            file_path_item = self.attachments_table.item(current_row, 4)
            if file_path_item:
                file_path = file_path_item.text()
                try:
                    import os
                    import subprocess
                    import platform

                    if os.path.exists(file_path):
                        # فتح الملف بالتطبيق الافتراضي
                        if platform.system() == 'Windows':
                            os.startfile(file_path)
                        elif platform.system() == 'Darwin':  # macOS
                            subprocess.call(['open', file_path])
                        else:  # Linux
                            subprocess.call(['xdg-open', file_path])
                    else:
                        QMessageBox.warning(self, "خطأ", "الملف غير موجود")

                except Exception as e:
                    QMessageBox.warning(self, "خطأ", f"فشل في فتح الملف: {str(e)}")
        else:
            QMessageBox.information(self, "تنبيه", "الرجاء اختيار ملف من الجدول")

    def download_attachment(self):
        """تحميل نسخة من الملف"""
        current_row = self.attachments_table.currentRow()
        if current_row >= 0:
            file_path_item = self.attachments_table.item(current_row, 4)
            file_name_item = self.attachments_table.item(current_row, 1)

            if file_path_item and file_name_item:
                source_path = file_path_item.text()
                original_name = file_name_item.text()

                try:
                    from PySide6.QtWidgets import QFileDialog
                    import shutil

                    # اختيار مكان الحفظ
                    save_path, _ = QFileDialog.getSaveFileName(
                        self,
                        "حفظ الملف",
                        original_name,
                        "جميع الملفات (*.*)"
                    )

                    if save_path:
                        shutil.copy2(source_path, save_path)
                        QMessageBox.information(self, "نجح", f"تم حفظ الملف في: {save_path}")

                except Exception as e:
                    QMessageBox.warning(self, "خطأ", f"فشل في حفظ الملف: {str(e)}")
        else:
            QMessageBox.information(self, "تنبيه", "الرجاء اختيار ملف من الجدول")

    def delete_attachment(self):
        """حذف الملف المحدد"""
        current_row = self.attachments_table.currentRow()
        if current_row >= 0:
            file_name_item = self.attachments_table.item(current_row, 1)
            file_path_item = self.attachments_table.item(current_row, 4)

            if file_name_item:
                file_name = file_name_item.text()

                reply = QMessageBox.question(
                    self,
                    "تأكيد الحذف",
                    f"هل أنت متأكد من حذف الملف '{file_name}'؟\n\nسيتم حذف الملف نهائياً من النظام.",
                    QMessageBox.Yes | QMessageBox.No,
                    QMessageBox.No
                )

                if reply == QMessageBox.Yes:
                    try:
                        # حذف الملف من النظام
                        if file_path_item:
                            file_path = file_path_item.text()
                            import os
                            if os.path.exists(file_path):
                                os.remove(file_path)

                        # حذف الصف من الجدول
                        self.attachments_table.removeRow(current_row)

                        QMessageBox.information(self, "نجح", f"تم حذف الملف '{file_name}' بنجاح")

                    except Exception as e:
                        QMessageBox.warning(self, "خطأ", f"فشل في حذف الملف: {str(e)}")
        else:
            QMessageBox.information(self, "تنبيه", "الرجاء اختيار ملف من الجدول")

    # ==================== التابات الخاصة بالمقاولات ====================

    def create_expenses_tab(self):
        """إنشاء تاب المصروفات"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # تخطيط أفقي للأزرار وشريط البحث
        top_layout = QHBoxLayout()

        # أزرار العمليات (في الجانب الأيمن)
        buttons_layout = QHBoxLayout()

        add_expense_btn = QPushButton("إضافة")
        add_expense_btn.setIcon(qta.icon('fa5s.plus'))
        add_expense_btn.clicked.connect(self.add_expense)
        add_expense_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #2ecc71;
            }
        """)
        buttons_layout.addWidget(add_expense_btn)

        edit_expense_btn = QPushButton("تعديل")
        edit_expense_btn.setIcon(qta.icon('fa5s.edit'))
        edit_expense_btn.clicked.connect(self.edit_expense)
        edit_expense_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #5dade2;
            }
        """)
        buttons_layout.addWidget(edit_expense_btn)

        delete_expense_btn = QPushButton("حذف")
        delete_expense_btn.setIcon(qta.icon('fa5s.trash'))
        delete_expense_btn.clicked.connect(self.delete_expense)
        delete_expense_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #ec7063;
            }
        """)
        buttons_layout.addWidget(delete_expense_btn)

        top_layout.addLayout(buttons_layout)

        # مساحة فارغة
        top_layout.addStretch()

        # ComboBox للفلترة حسب رقم العهدة
        filter_layout = QHBoxLayout()
        filter_layout.addWidget(QLabel("فلترة حسب رقم العهدة:"))
        self.expenses_custody_filter_combo = QComboBox()
        self.expenses_custody_filter_combo.addItem("جميع العهد")
        self.expenses_custody_filter_combo.currentTextChanged.connect(self.filter_expenses_by_custody)
        filter_layout.addWidget(self.expenses_custody_filter_combo)

        # شريط البحث (في الجانب الأيسر)
        search_layout = QHBoxLayout()
        search_layout.addWidget(QLabel("البحث:"))
        self.expenses_search = QLineEdit()
        self.expenses_search.setPlaceholderText("ابحث في المصروفات...")
        self.expenses_search.textChanged.connect(self.filter_expenses)
        search_layout.addWidget(self.expenses_search)

        filter_layout.addLayout(search_layout)
        top_layout.addLayout(filter_layout)
        layout.addLayout(top_layout)

        # جدول المصروفات
        self.expenses_table = QTableWidget()
        self.setup_expenses_table()
        layout.addWidget(self.expenses_table)

        self.tab_widget.addTab(tab, qta.icon('fa5s.calculator', color='#e74c3c'), "المصروفات")

    def setup_expenses_table(self):
        """إعداد جدول المصروفات"""
        headers = ["ID", "الرقم", "وصف المصروف", "المبلغ", "تاريخ المصروف", "المستلم", "طريقة الدفع", "رقم الفاتورة", "المورد", "فئة المصروف"]
        self.expenses_table.setColumnCount(len(headers))
        self.expenses_table.setHorizontalHeaderLabels(headers)
        self.expenses_table.hideColumn(0)  # إخفاء عمود ID

        # تطبيق إعدادات الجدول
        table_setting(self.expenses_table)

        # إضافة وظيفة النقر المزدوج لفتح حوار التعديل
        self.expenses_table.itemDoubleClicked.connect(self.on_expenses_table_double_click)

    def create_custody_tab(self):
        """إنشاء تاب العهد المالية"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # تخطيط أفقي للأزرار وشريط البحث
        top_layout = QHBoxLayout()

        # أزرار العمليات (في الجانب الأيمن)
        buttons_layout = QHBoxLayout()

        add_custody_btn = QPushButton("إضافة")
        add_custody_btn.setIcon(qta.icon('fa5s.plus'))
        add_custody_btn.clicked.connect(self.add_custody)
        add_custody_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #2ecc71;
            }
        """)
        buttons_layout.addWidget(add_custody_btn)

        edit_custody_btn = QPushButton("تعديل")
        edit_custody_btn.setIcon(qta.icon('fa5s.edit'))
        edit_custody_btn.clicked.connect(self.edit_custody)
        edit_custody_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #5dade2;
            }
        """)
        buttons_layout.addWidget(edit_custody_btn)

        delete_custody_btn = QPushButton("حذف")
        delete_custody_btn.setIcon(qta.icon('fa5s.trash'))
        delete_custody_btn.clicked.connect(self.delete_custody)
        delete_custody_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #ec7063;
            }
        """)
        buttons_layout.addWidget(delete_custody_btn)

        # إضافة الأزرار الجديدة لتاب العهد المالية
        transfer_custody_btn = QPushButton("ترحيل العهدة")
        transfer_custody_btn.setIcon(qta.icon('fa5s.exchange-alt'))
        transfer_custody_btn.clicked.connect(self.transfer_custody)
        transfer_custody_btn.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
        """)
        buttons_layout.addWidget(transfer_custody_btn)

        close_custody_btn = QPushButton("إغلاق العهدة")
        close_custody_btn.setIcon(qta.icon('fa5s.lock'))
        close_custody_btn.clicked.connect(self.close_custody)
        close_custody_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        buttons_layout.addWidget(close_custody_btn)

        top_layout.addLayout(buttons_layout)

        # مساحة فارغة
        top_layout.addStretch()

        # ComboBox للفلترة حسب رقم العهدة
        filter_layout = QHBoxLayout()
        filter_layout.addWidget(QLabel("فلترة حسب رقم العهدة:"))
        self.custody_filter_combo = QComboBox()
        self.custody_filter_combo.addItem("جميع العهد")
        self.custody_filter_combo.currentTextChanged.connect(self.filter_custody_by_number)
        filter_layout.addWidget(self.custody_filter_combo)

        # شريط البحث (في الجانب الأيسر)
        search_layout = QHBoxLayout()
        search_layout.addWidget(QLabel("البحث:"))
        self.custody_search = QLineEdit()
        self.custody_search.setPlaceholderText("ابحث في العهد المالية...")
        self.custody_search.textChanged.connect(self.filter_custody)
        search_layout.addWidget(self.custody_search)

        filter_layout.addLayout(search_layout)
        top_layout.addLayout(filter_layout)
        layout.addLayout(top_layout)

        # جدول العهد المالية
        self.custody_table = QTableWidget()
        self.setup_custody_table()
        layout.addWidget(self.custody_table)

        self.tab_widget.addTab(tab, qta.icon('fa5s.credit-card', color='#27ae60'), "العهد المالية")

    def setup_custody_table(self):
        """إعداد جدول العهد المالية"""
        headers = ["ID", "الرقم", "رقم العهدة", "وصف العهدة", "مبلغ العهدة", "نسبة المكتب", "تاريخ الاستلام", "حالة العهدة", "المصروف", "المتبقي"]
        self.custody_table.setColumnCount(len(headers))
        self.custody_table.setHorizontalHeaderLabels(headers)
        self.custody_table.hideColumn(0)  # إخفاء عمود ID

        # تطبيق إعدادات الجدول
        table_setting(self.custody_table)

        # إضافة وظيفة النقر المزدوج لفتح حوار التعديل
        self.custody_table.itemDoubleClicked.connect(self.on_custody_table_double_click)

    def create_custody_payments_tab(self):
        """إنشاء تاب دفعات العهد"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # تخطيط أفقي للأزرار وشريط البحث
        top_layout = QHBoxLayout()

        # أزرار العمليات (في الجانب الأيمن)
        buttons_layout = QHBoxLayout()

        add_custody_payment_btn = QPushButton("إضافة")
        add_custody_payment_btn.setIcon(qta.icon('fa5s.plus'))
        add_custody_payment_btn.clicked.connect(self.add_custody_payment)
        add_custody_payment_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #2ecc71;
            }
        """)
        buttons_layout.addWidget(add_custody_payment_btn)

        edit_custody_payment_btn = QPushButton("تعديل")
        edit_custody_payment_btn.setIcon(qta.icon('fa5s.edit'))
        edit_custody_payment_btn.clicked.connect(self.edit_custody_payment)
        edit_custody_payment_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #5dade2;
            }
        """)
        buttons_layout.addWidget(edit_custody_payment_btn)

        delete_custody_payment_btn = QPushButton("حذف")
        delete_custody_payment_btn.setIcon(qta.icon('fa5s.trash'))
        delete_custody_payment_btn.clicked.connect(self.delete_custody_payment)
        delete_custody_payment_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #ec7063;
            }
        """)
        buttons_layout.addWidget(delete_custody_payment_btn)

        top_layout.addLayout(buttons_layout)

        # مساحة فارغة
        top_layout.addStretch()

        # شريط البحث (في الجانب الأيسر)
        search_layout = QHBoxLayout()
        search_layout.addWidget(QLabel("البحث:"))
        self.custody_payments_search = QLineEdit()
        self.custody_payments_search.setPlaceholderText("ابحث في دفعات العهد...")
        self.custody_payments_search.textChanged.connect(self.filter_custody_payments)
        search_layout.addWidget(self.custody_payments_search)

        top_layout.addLayout(search_layout)
        layout.addLayout(top_layout)

        # جدول دفعات العهد
        self.custody_payments_table = QTableWidget()
        self.setup_custody_payments_table()
        layout.addWidget(self.custody_payments_table)

        self.tab_widget.addTab(tab, qta.icon('fa5s.money-bill', color='#f39c12'), "دفعات العهد")

    def setup_custody_payments_table(self):
        """إعداد جدول دفعات العهد"""
        headers = ["ID", "الرقم", "رقم العهدة", "وصف الدفعة", "المبلغ", "تاريخ الدفعة", "نوع الدفعة", "طريقة الدفع", "المستلم"]
        self.custody_payments_table.setColumnCount(len(headers))
        self.custody_payments_table.setHorizontalHeaderLabels(headers)
        self.custody_payments_table.hideColumn(0)  # إخفاء عمود ID

        # تطبيق إعدادات الجدول
        table_setting(self.custody_payments_table)

        # إضافة وظيفة النقر المزدوج لفتح حوار التعديل
        self.custody_payments_table.itemDoubleClicked.connect(self.on_custody_payments_table_double_click)

    def create_contractors_tab(self):
        """إنشاء تاب المقاولين"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # تخطيط أفقي للأزرار وشريط البحث
        top_layout = QHBoxLayout()

        # أزرار العمليات (في الجانب الأيمن)
        buttons_layout = QHBoxLayout()

        add_contractor_btn = QPushButton("إضافة")
        add_contractor_btn.setIcon(qta.icon('fa5s.plus'))
        add_contractor_btn.clicked.connect(self.add_contractor)
        add_contractor_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #2ecc71;
            }
        """)
        buttons_layout.addWidget(add_contractor_btn)

        edit_contractor_btn = QPushButton("تعديل")
        edit_contractor_btn.setIcon(qta.icon('fa5s.edit'))
        edit_contractor_btn.clicked.connect(self.edit_contractor)
        edit_contractor_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #5dade2;
            }
        """)
        buttons_layout.addWidget(edit_contractor_btn)

        delete_contractor_btn = QPushButton("حذف")
        delete_contractor_btn.setIcon(qta.icon('fa5s.trash'))
        delete_contractor_btn.clicked.connect(self.delete_contractor)
        delete_contractor_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #ec7063;
            }
        """)
        buttons_layout.addWidget(delete_contractor_btn)

        # إضافة الأزرار الجديدة لتاب المقاولين
        insert_balance_btn = QPushButton("إدراج الرصيد")
        insert_balance_btn.setIcon(qta.icon('fa5s.user-plus'))
        insert_balance_btn.clicked.connect(self.insert_contractor_balance)
        insert_balance_btn.setStyleSheet("""
            QPushButton {
                background-color: #16a085;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #138d75;
            }
        """)
        buttons_layout.addWidget(insert_balance_btn)

        insert_all_balances_btn = QPushButton("إدراج جميع الأرصدة")
        insert_all_balances_btn.setIcon(qta.icon('fa5s.users-cog'))
        insert_all_balances_btn.clicked.connect(self.insert_all_contractor_balances)
        insert_all_balances_btn.setStyleSheet("""
            QPushButton {
                background-color: #8e44ad;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #9b59b6;
            }
        """)
        buttons_layout.addWidget(insert_all_balances_btn)

        top_layout.addLayout(buttons_layout)

        # مساحة فارغة
        top_layout.addStretch()

        # ComboBox للفلترة حسب اسم المقاول
        filter_layout = QHBoxLayout()
        filter_layout.addWidget(QLabel("فلترة حسب المقاول:"))
        self.contractors_filter_combo = QComboBox()
        self.contractors_filter_combo.addItem("جميع المقاولين")
        self.contractors_filter_combo.currentTextChanged.connect(self.filter_contractors_by_name)
        filter_layout.addWidget(self.contractors_filter_combo)

        # شريط البحث (في الجانب الأيسر)
        search_layout = QHBoxLayout()
        search_layout.addWidget(QLabel("البحث:"))
        self.contractors_search = QLineEdit()
        self.contractors_search.setPlaceholderText("ابحث في المقاولين...")
        self.contractors_search.textChanged.connect(self.filter_contractors)
        search_layout.addWidget(self.contractors_search)

        filter_layout.addLayout(search_layout)
        top_layout.addLayout(filter_layout)
        layout.addLayout(top_layout)

        # جدول المقاولين
        self.contractors_table = QTableWidget()
        self.setup_contractors_table()
        layout.addWidget(self.contractors_table)

        self.tab_widget.addTab(tab, qta.icon('fa5s.hard-hat', color='#8b4513'), "المقاولين")

    def setup_contractors_table(self):
        """إعداد جدول المقاولين"""
        headers = ["ID", "الرقم", "اسم المقاول", "التخصص", "رقم الهاتف", "العنوان", "تقييم الأداء", "الحالة"]
        self.contractors_table.setColumnCount(len(headers))
        self.contractors_table.setHorizontalHeaderLabels(headers)
        self.contractors_table.hideColumn(0)  # إخفاء عمود ID

        # تطبيق إعدادات الجدول
        table_setting(self.contractors_table)

        # إضافة وظيفة النقر المزدوج لفتح حوار التعديل
        self.contractors_table.itemDoubleClicked.connect(self.on_contractors_table_double_click)

    def create_workers_tab(self):
        """إنشاء تاب العمال"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # تخطيط أفقي للأزرار وشريط البحث
        top_layout = QHBoxLayout()

        # أزرار العمليات (في الجانب الأيمن)
        buttons_layout = QHBoxLayout()

        add_worker_btn = QPushButton("إضافة")
        add_worker_btn.setIcon(qta.icon('fa5s.plus'))
        add_worker_btn.clicked.connect(self.add_worker)
        add_worker_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #2ecc71;
            }
        """)
        buttons_layout.addWidget(add_worker_btn)

        edit_worker_btn = QPushButton("تعديل")
        edit_worker_btn.setIcon(qta.icon('fa5s.edit'))
        edit_worker_btn.clicked.connect(self.edit_worker)
        edit_worker_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #5dade2;
            }
        """)
        buttons_layout.addWidget(edit_worker_btn)

        delete_worker_btn = QPushButton("حذف")
        delete_worker_btn.setIcon(qta.icon('fa5s.trash'))
        delete_worker_btn.clicked.connect(self.delete_worker)
        delete_worker_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #ec7063;
            }
        """)
        buttons_layout.addWidget(delete_worker_btn)

        # إضافة الأزرار الجديدة لتاب العمال
        insert_balance_btn = QPushButton("إدراج الرصيد")
        insert_balance_btn.setIcon(qta.icon('fa5s.user-plus'))
        insert_balance_btn.clicked.connect(self.insert_worker_balance)
        insert_balance_btn.setStyleSheet("""
            QPushButton {
                background-color: #16a085;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #138d75;
            }
        """)
        buttons_layout.addWidget(insert_balance_btn)

        insert_all_balances_btn = QPushButton("إدراج جميع الأرصدة")
        insert_all_balances_btn.setIcon(qta.icon('fa5s.users-cog'))
        insert_all_balances_btn.clicked.connect(self.insert_all_worker_balances)
        insert_all_balances_btn.setStyleSheet("""
            QPushButton {
                background-color: #8e44ad;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #9b59b6;
            }
        """)
        buttons_layout.addWidget(insert_all_balances_btn)

        top_layout.addLayout(buttons_layout)

        # مساحة فارغة
        top_layout.addStretch()

        # ComboBox للفلترة حسب اسم العامل
        filter_layout = QHBoxLayout()
        filter_layout.addWidget(QLabel("فلترة حسب العامل:"))
        self.workers_filter_combo = QComboBox()
        self.workers_filter_combo.addItem("جميع العمال")
        self.workers_filter_combo.currentTextChanged.connect(self.filter_workers_by_name)
        filter_layout.addWidget(self.workers_filter_combo)

        # شريط البحث (في الجانب الأيسر)
        search_layout = QHBoxLayout()
        search_layout.addWidget(QLabel("البحث:"))
        self.workers_search = QLineEdit()
        self.workers_search.setPlaceholderText("ابحث في العمال...")
        self.workers_search.textChanged.connect(self.filter_workers)
        search_layout.addWidget(self.workers_search)

        filter_layout.addLayout(search_layout)
        top_layout.addLayout(filter_layout)
        layout.addLayout(top_layout)

        # جدول العمال
        self.workers_table = QTableWidget()
        self.setup_workers_table()
        layout.addWidget(self.workers_table)

        self.tab_widget.addTab(tab, qta.icon('fa5s.tools', color='#95a5a6'), "العمال")

    def setup_workers_table(self):
        """إعداد جدول العمال"""
        headers = ["ID", "الرقم", "اسم العامل", "التخصص", "الراتب اليومي", "أيام العمل", "الإجمالي", "المدفوع", "المتبقي"]
        self.workers_table.setColumnCount(len(headers))
        self.workers_table.setHorizontalHeaderLabels(headers)
        self.workers_table.hideColumn(0)  # إخفاء عمود ID

        # تطبيق إعدادات الجدول
        table_setting(self.workers_table)

        # إضافة وظيفة النقر المزدوج لفتح حوار التعديل
        self.workers_table.itemDoubleClicked.connect(self.on_workers_table_double_click)

    def create_suppliers_tab(self):
        """إنشاء تاب الموردين"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # تخطيط أفقي للأزرار وشريط البحث
        top_layout = QHBoxLayout()

        # أزرار العمليات (في الجانب الأيمن)
        buttons_layout = QHBoxLayout()

        add_supplier_btn = QPushButton("إضافة")
        add_supplier_btn.setIcon(qta.icon('fa5s.plus'))
        add_supplier_btn.clicked.connect(self.add_supplier)
        add_supplier_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #2ecc71;
            }
        """)
        buttons_layout.addWidget(add_supplier_btn)

        edit_supplier_btn = QPushButton("تعديل")
        edit_supplier_btn.setIcon(qta.icon('fa5s.edit'))
        edit_supplier_btn.clicked.connect(self.edit_supplier)
        edit_supplier_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #5dade2;
            }
        """)
        buttons_layout.addWidget(edit_supplier_btn)

        delete_supplier_btn = QPushButton("حذف")
        delete_supplier_btn.setIcon(qta.icon('fa5s.trash'))
        delete_supplier_btn.clicked.connect(self.delete_supplier)
        delete_supplier_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #ec7063;
            }
        """)
        buttons_layout.addWidget(delete_supplier_btn)

        # إضافة الأزرار الجديدة لتاب الموردين
        insert_balance_btn = QPushButton("إدراج الرصيد")
        insert_balance_btn.setIcon(qta.icon('fa5s.user-plus'))
        insert_balance_btn.clicked.connect(self.insert_supplier_balance)
        insert_balance_btn.setStyleSheet("""
            QPushButton {
                background-color: #16a085;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #138d75;
            }
        """)
        buttons_layout.addWidget(insert_balance_btn)

        insert_all_balances_btn = QPushButton("إدراج جميع الأرصدة")
        insert_all_balances_btn.setIcon(qta.icon('fa5s.users-cog'))
        insert_all_balances_btn.clicked.connect(self.insert_all_supplier_balances)
        insert_all_balances_btn.setStyleSheet("""
            QPushButton {
                background-color: #8e44ad;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #9b59b6;
            }
        """)
        buttons_layout.addWidget(insert_all_balances_btn)

        top_layout.addLayout(buttons_layout)

        # مساحة فارغة
        top_layout.addStretch()

        # ComboBox للفلترة حسب اسم المورد
        filter_layout = QHBoxLayout()
        filter_layout.addWidget(QLabel("فلترة حسب المورد:"))
        self.suppliers_filter_combo = QComboBox()
        self.suppliers_filter_combo.addItem("جميع الموردين")
        self.suppliers_filter_combo.currentTextChanged.connect(self.filter_suppliers_by_name)
        filter_layout.addWidget(self.suppliers_filter_combo)

        # شريط البحث (في الجانب الأيسر)
        search_layout = QHBoxLayout()
        search_layout.addWidget(QLabel("البحث:"))
        self.suppliers_search = QLineEdit()
        self.suppliers_search.setPlaceholderText("ابحث في الموردين...")
        self.suppliers_search.textChanged.connect(self.filter_suppliers)
        search_layout.addWidget(self.suppliers_search)

        filter_layout.addLayout(search_layout)
        top_layout.addLayout(filter_layout)
        layout.addLayout(top_layout)

        # جدول الموردين
        self.suppliers_table = QTableWidget()
        self.setup_suppliers_table()
        layout.addWidget(self.suppliers_table)

        self.tab_widget.addTab(tab, qta.icon('fa5s.truck', color='#34495e'), "الموردين")

    def setup_suppliers_table(self):
        """إعداد جدول الموردين"""
        headers = ["ID", "الرقم", "اسم المورد", "نوع المواد", "رقم الهاتف", "العنوان", "تقييم الجودة", "الحالة"]
        self.suppliers_table.setColumnCount(len(headers))
        self.suppliers_table.setHorizontalHeaderLabels(headers)
        self.suppliers_table.hideColumn(0)  # إخفاء عمود ID

        # تطبيق إعدادات الجدول
        table_setting(self.suppliers_table)

        # إضافة وظيفة النقر المزدوج لفتح حوار التعديل
        self.suppliers_table.itemDoubleClicked.connect(self.on_suppliers_table_double_click)

    # ==================== دوال معالجة الأحداث للمقاولات ====================

    def filter_expenses(self):
        """تصفية المصروفات"""
        search_text = self.expenses_search.text().lower()
        for row in range(self.expenses_table.rowCount()):
            show_row = False
            for col in range(self.expenses_table.columnCount()):
                item = self.expenses_table.item(row, col)
                if item and search_text in item.text().lower():
                    show_row = True
                    break
            self.expenses_table.setRowHidden(row, not show_row)

    def filter_custody(self):
        """تصفية العهد المالية"""
        search_text = self.custody_search.text().lower()
        for row in range(self.custody_table.rowCount()):
            show_row = False
            for col in range(self.custody_table.columnCount()):
                item = self.custody_table.item(row, col)
                if item and search_text in item.text().lower():
                    show_row = True
                    break
            self.custody_table.setRowHidden(row, not show_row)

    def filter_custody_payments(self):
        """تصفية دفعات العهد"""
        search_text = self.custody_payments_search.text().lower()
        for row in range(self.custody_payments_table.rowCount()):
            show_row = False
            for col in range(self.custody_payments_table.columnCount()):
                item = self.custody_payments_table.item(row, col)
                if item and search_text in item.text().lower():
                    show_row = True
                    break
            self.custody_payments_table.setRowHidden(row, not show_row)

    def filter_contractors(self):
        """تصفية المقاولين"""
        search_text = self.contractors_search.text().lower()
        for row in range(self.contractors_table.rowCount()):
            show_row = False
            for col in range(self.contractors_table.columnCount()):
                item = self.contractors_table.item(row, col)
                if item and search_text in item.text().lower():
                    show_row = True
                    break
            self.contractors_table.setRowHidden(row, not show_row)

    def filter_workers(self):
        """تصفية العمال"""
        search_text = self.workers_search.text().lower()
        for row in range(self.workers_table.rowCount()):
            show_row = False
            for col in range(self.workers_table.columnCount()):
                item = self.workers_table.item(row, col)
                if item and search_text in item.text().lower():
                    show_row = True
                    break
            self.workers_table.setRowHidden(row, not show_row)

    def filter_suppliers(self):
        """تصفية الموردين"""
        search_text = self.suppliers_search.text().lower()
        for row in range(self.suppliers_table.rowCount()):
            show_row = False
            for col in range(self.suppliers_table.columnCount()):
                item = self.suppliers_table.item(row, col)
                if item and search_text in item.text().lower():
                    show_row = True
                    break
            self.suppliers_table.setRowHidden(row, not show_row)

    # دوال إضافة/تعديل/حذف للمقاولات
    def add_expense(self):
        """إضافة مصروف"""
        QMessageBox.information(self, "إضافة مصروف", "سيتم فتح نافذة إضافة مصروف جديد")

    def edit_expense(self):
        """تعديل مصروف"""
        QMessageBox.information(self, "تعديل مصروف", "سيتم فتح نافذة تعديل المصروف")

    def delete_expense(self):
        """حذف مصروف"""
        QMessageBox.information(self, "حذف مصروف", "سيتم حذف المصروف المحدد")

    def add_custody(self):
        """إضافة عهدة"""
        QMessageBox.information(self, "إضافة عهدة", "سيتم فتح نافذة إضافة عهدة جديدة")

    def edit_custody(self):
        """تعديل عهدة"""
        QMessageBox.information(self, "تعديل عهدة", "سيتم فتح نافذة تعديل العهدة")

    def delete_custody(self):
        """حذف عهدة"""
        QMessageBox.information(self, "حذف عهدة", "سيتم حذف العهدة المحددة")

    def add_custody_payment(self):
        """إضافة دفعة عهد"""
        QMessageBox.information(self, "إضافة دفعة عهد", "سيتم فتح نافذة إضافة دفعة عهد جديدة")

    def edit_custody_payment(self):
        """تعديل دفعة عهد"""
        QMessageBox.information(self, "تعديل دفعة عهد", "سيتم فتح نافذة تعديل دفعة العهد")

    def delete_custody_payment(self):
        """حذف دفعة عهد"""
        QMessageBox.information(self, "حذف دفعة عهد", "سيتم حذف دفعة العهد المحددة")

    def add_contractor(self):
        """إضافة مقاول"""
        QMessageBox.information(self, "إضافة مقاول", "سيتم فتح نافذة إضافة مقاول جديد")

    def edit_contractor(self):
        """تعديل مقاول"""
        QMessageBox.information(self, "تعديل مقاول", "سيتم فتح نافذة تعديل المقاول")

    def delete_contractor(self):
        """حذف مقاول"""
        QMessageBox.information(self, "حذف مقاول", "سيتم حذف المقاول المحدد")

    def add_worker(self):
        """إضافة عامل"""
        QMessageBox.information(self, "إضافة عامل", "سيتم فتح نافذة إضافة عامل جديد")

    def edit_worker(self):
        """تعديل عامل"""
        QMessageBox.information(self, "تعديل عامل", "سيتم فتح نافذة تعديل العامل")

    def delete_worker(self):
        """حذف عامل"""
        QMessageBox.information(self, "حذف عامل", "سيتم حذف العامل المحدد")

    def add_supplier(self):
        """إضافة مورد"""
        QMessageBox.information(self, "إضافة مورد", "سيتم فتح نافذة إضافة مورد جديد")

    def edit_supplier(self):
        """تعديل مورد"""
        QMessageBox.information(self, "تعديل مورد", "سيتم فتح نافذة تعديل المورد")

    def delete_supplier(self):
        """حذف مورد"""
        QMessageBox.information(self, "حذف مورد", "سيتم حذف المورد المحدد")

    # ==================== دوال الأزرار الجديدة ====================

    # دوال تاب مراحل المشروع
    def insert_phase_amount(self):
        """إدراج مبلغ لمرحلة محددة إلى إجمالي قيمة المشروع"""
        current_row = self.phases_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد مرحلة لإدراج المبلغ")
            return

        try:
            # الحصول على معرف المرحلة والمبلغ
            phase_id_item = self.phases_table.item(current_row, 0)
            amount_item = self.phases_table.item(current_row, 7)  # عمود الإجمالي
            status_item = self.phases_table.item(current_row, 9)  # عمود حالة المبلغ

            if not phase_id_item or not amount_item:
                QMessageBox.warning(self, "خطأ", "لا يمكن الحصول على بيانات المرحلة")
                return

            phase_id = phase_id_item.text()
            amount = float(amount_item.text())
            current_status = status_item.text() if status_item else "غير مدرج"

            if current_status == "تم الإدراج":
                QMessageBox.information(self, "معلومات", "تم إدراج مبلغ هذه المرحلة مسبقاً")
                return

            # تأكيد العملية
            reply = QMessageBox.question(
                self, "تأكيد الإدراج",
                f"هل تريد إدراج مبلغ {amount:,.2f} إلى إجمالي قيمة المشروع؟",
                QMessageBox.Yes | QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                conn = mysql.connector.connect(
                    host=host, user=user_r, password=password_r,
                    database="project_manager_V2"
                )
                cursor = conn.cursor()

                # تحديث حالة المبلغ في جدول المراحل
                cursor.execute("""
                    UPDATE المشاريع_المراحل
                    SET حالة_المبلغ = 'تم الإدراج'
                    WHERE id = %s
                """, (phase_id,))

                # تحديث إجمالي قيمة المشروع
                cursor.execute("""
                    UPDATE المشاريع
                    SET المبلغ = المبلغ + %s
                    WHERE id = %s
                """, (amount, self.project_id))

                conn.commit()
                conn.close()

                QMessageBox.information(self, "نجح", f"تم إدراج المبلغ {amount:,.2f} بنجاح")
                self.load_phases_data()
                self.load_project_info()

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في إدراج المبلغ: {str(e)}")

    def insert_all_phase_amounts(self):
        """إدراج المبالغ لجميع المراحل دفعة واحدة إلى إجمالي قيمة المشروع"""
        try:
            conn = mysql.connector.connect(
                host=host, user=user_r, password=password_r,
                database="project_manager_V2"
            )
            cursor = conn.cursor()

            # جلب المراحل غير المدرجة
            cursor.execute("""
                SELECT id, الإجمالي FROM المشاريع_المراحل
                WHERE معرف_المشروع = %s AND حالة_المبلغ = 'غير مدرج'
            """, (self.project_id,))

            phases = cursor.fetchall()

            if not phases:
                QMessageBox.information(self, "معلومات", "جميع المراحل تم إدراجها مسبقاً")
                conn.close()
                return

            total_amount = sum(phase[1] for phase in phases)

            # تأكيد العملية
            reply = QMessageBox.question(
                self, "تأكيد الإدراج",
                f"هل تريد إدراج إجمالي مبلغ {total_amount:,.2f} من {len(phases)} مرحلة إلى إجمالي قيمة المشروع؟",
                QMessageBox.Yes | QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                # تحديث حالة جميع المراحل
                phase_ids = [str(phase[0]) for phase in phases]
                cursor.execute(f"""
                    UPDATE المشاريع_المراحل
                    SET حالة_المبلغ = 'تم الإدراج'
                    WHERE id IN ({','.join(['%s'] * len(phase_ids))})
                """, phase_ids)

                # تحديث إجمالي قيمة المشروع
                cursor.execute("""
                    UPDATE المشاريع
                    SET المبلغ = المبلغ + %s
                    WHERE id = %s
                """, (total_amount, self.project_id))

                conn.commit()
                conn.close()

                QMessageBox.information(self, "نجح", f"تم إدراج إجمالي مبلغ {total_amount:,.2f} من {len(phases)} مرحلة بنجاح")
                self.load_phases_data()
                self.load_project_info()
            else:
                conn.close()

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في إدراج المبالغ: {str(e)}")

    def filter_phases_by_name(self):
        """فلترة المراحل حسب اسم المرحلة"""
        selected_phase = self.phases_filter_combo.currentText()

        for row in range(self.phases_table.rowCount()):
            if selected_phase == "جميع المراحل":
                self.phases_table.setRowHidden(row, False)
            else:
                phase_name_item = self.phases_table.item(row, 2)  # عمود اسم المرحلة
                if phase_name_item:
                    show_row = selected_phase in phase_name_item.text()
                    self.phases_table.setRowHidden(row, not show_row)
                else:
                    self.phases_table.setRowHidden(row, True)

    # دوال تاب مهام المهندسين
    def insert_engineer_balance(self):
        """إدراج رصيد لمهندس محدد"""
        current_row = self.engineers_tasks_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد مهمة مهندس لإدراج الرصيد")
            return

        try:
            # الحصول على معرف المهندس والمبلغ
            engineer_id_item = self.engineers_tasks_table.item(current_row, 0)
            amount_item = self.engineers_tasks_table.item(current_row, 5)  # عمود مبلغ المهندس
            status_item = self.engineers_tasks_table.item(current_row, 6)  # عمود حالة المبلغ

            if not engineer_id_item or not amount_item:
                QMessageBox.warning(self, "خطأ", "لا يمكن الحصول على بيانات المهندس")
                return

            engineer_id = engineer_id_item.text()
            amount = float(amount_item.text())
            current_status = status_item.text() if status_item else "غير مدرج"

            if current_status == "تم الإدراج":
                QMessageBox.information(self, "معلومات", "تم إدراج رصيد هذا المهندس مسبقاً")
                return

            # تأكيد العملية
            reply = QMessageBox.question(
                self, "تأكيد الإدراج",
                f"هل تريد إدراج مبلغ {amount:,.2f} إلى رصيد المهندس؟",
                QMessageBox.Yes | QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                conn = mysql.connector.connect(
                    host=host, user=user_r, password=password_r,
                    database="project_manager_V2"
                )
                cursor = conn.cursor()

                # تحديث حالة المبلغ في جدول مهام المهندسين
                cursor.execute("""
                    UPDATE المشاريع_مهام_المهندسين
                    SET حالة_مبلغ_المهندس = 'تم الإدراج'
                    WHERE id = %s
                """, (engineer_id,))

                # إضافة المبلغ إلى رصيد المهندس
                cursor.execute("""
                    UPDATE الموظفين
                    SET الرصيد = الرصيد + %s
                    WHERE id = (
                        SELECT معرف_المهندس FROM المشاريع_مهام_المهندسين WHERE id = %s
                    )
                """, (amount, engineer_id))

                conn.commit()
                conn.close()

                QMessageBox.information(self, "نجح", f"تم إدراج المبلغ {amount:,.2f} إلى رصيد المهندس بنجاح")
                self.load_engineers_tasks_data()

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في إدراج الرصيد: {str(e)}")

    def insert_all_engineer_balances(self):
        """إدراج الأرصدة لجميع المهندسين"""
        try:
            conn = mysql.connector.connect(
                host=host, user=user_r, password=password_r,
                database="project_manager_V2"
            )
            cursor = conn.cursor()

            # جلب مهام المهندسين غير المدرجة
            cursor.execute("""
                SELECT id, معرف_المهندس, مبلغ_المهندس
                FROM المشاريع_مهام_المهندسين
                WHERE معرف_المرحلة IN (
                    SELECT id FROM المشاريع_المراحل WHERE معرف_المشروع = %s
                ) AND حالة_مبلغ_المهندس = 'غير مدرج'
            """, (self.project_id,))

            tasks = cursor.fetchall()

            if not tasks:
                QMessageBox.information(self, "معلومات", "جميع أرصدة المهندسين تم إدراجها مسبقاً")
                conn.close()
                return

            total_amount = sum(task[2] for task in tasks)

            # تأكيد العملية
            reply = QMessageBox.question(
                self, "تأكيد الإدراج",
                f"هل تريد إدراج إجمالي مبلغ {total_amount:,.2f} لأرصدة {len(tasks)} مهندس؟",
                QMessageBox.Yes | QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                # تحديث حالة جميع المهام
                for task in tasks:
                    task_id, engineer_id, amount = task

                    # تحديث حالة المبلغ
                    cursor.execute("""
                        UPDATE المشاريع_مهام_المهندسين
                        SET حالة_مبلغ_المهندس = 'تم الإدراج'
                        WHERE id = %s
                    """, (task_id,))

                    # إضافة المبلغ إلى رصيد المهندس
                    cursor.execute("""
                        UPDATE الموظفين
                        SET الرصيد = الرصيد + %s
                        WHERE id = %s
                    """, (amount, engineer_id))

                conn.commit()
                conn.close()

                QMessageBox.information(self, "نجح", f"تم إدراج إجمالي مبلغ {total_amount:,.2f} لأرصدة {len(tasks)} مهندس بنجاح")
                self.load_engineers_tasks_data()
            else:
                conn.close()

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في إدراج الأرصدة: {str(e)}")

    def filter_engineers_by_name(self):
        """فلترة المهندسين حسب اسم المهندس"""
        selected_engineer = self.engineers_filter_combo.currentText()

        for row in range(self.engineers_tasks_table.rowCount()):
            if selected_engineer == "جميع المهندسين":
                self.engineers_tasks_table.setRowHidden(row, False)
            else:
                engineer_name_item = self.engineers_tasks_table.item(row, 2)  # عمود المهندس
                if engineer_name_item:
                    show_row = selected_engineer in engineer_name_item.text()
                    self.engineers_tasks_table.setRowHidden(row, not show_row)
                else:
                    self.engineers_tasks_table.setRowHidden(row, True)

    # دوال تاب المقاولين
    def insert_contractor_balance(self):
        """إدراج رصيد لمقاول محدد"""
        current_row = self.contractors_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد مقاول لإدراج الرصيد")
            return

        try:
            # الحصول على معرف المقاول والمبلغ
            contractor_id_item = self.contractors_table.item(current_row, 0)
            amount_item = self.contractors_table.item(current_row, 5)  # عمود مبلغ المقاول
            status_item = self.contractors_table.item(current_row, 6)  # عمود حالة المبلغ

            if not contractor_id_item or not amount_item:
                QMessageBox.warning(self, "خطأ", "لا يمكن الحصول على بيانات المقاول")
                return

            contractor_id = contractor_id_item.text()
            amount = float(amount_item.text())
            current_status = status_item.text() if status_item else "غير مدرج"

            if current_status == "تم الإدراج":
                QMessageBox.information(self, "معلومات", "تم إدراج رصيد هذا المقاول مسبقاً")
                return

            # تأكيد العملية
            reply = QMessageBox.question(
                self, "تأكيد الإدراج",
                f"هل تريد إدراج مبلغ {amount:,.2f} إلى رصيد المقاول؟",
                QMessageBox.Yes | QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                conn = mysql.connector.connect(
                    host=host, user=user_r, password=password_r,
                    database="project_manager_V2"
                )
                cursor = conn.cursor()

                # تحديث حالة المبلغ في جدول مهام المقاولين
                cursor.execute("""
                    UPDATE المقاولات_مهام_المقاولين
                    SET حالة_مبلغ_المقاول = 'تم الإدراج'
                    WHERE id = %s
                """, (contractor_id,))

                # إضافة المبلغ إلى رصيد المقاول
                cursor.execute("""
                    UPDATE المقاولين
                    SET الرصيد = الرصيد + %s
                    WHERE id = (
                        SELECT معرف_المقاول FROM المقاولات_مهام_المقاولين WHERE id = %s
                    )
                """, (amount, contractor_id))

                conn.commit()
                conn.close()

                QMessageBox.information(self, "نجح", f"تم إدراج المبلغ {amount:,.2f} إلى رصيد المقاول بنجاح")
                self.load_contractors_data()

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في إدراج الرصيد: {str(e)}")

    def insert_all_contractor_balances(self):
        """إدراج الأرصدة لجميع المقاولين"""
        try:
            conn = mysql.connector.connect(
                host=host, user=user_r, password=password_r,
                database="project_manager_V2"
            )
            cursor = conn.cursor()

            # جلب مهام المقاولين غير المدرجة
            cursor.execute("""
                SELECT id, معرف_المقاول, مبلغ_المقاول
                FROM المقاولات_مهام_المقاولين
                WHERE معرف_المرحلة IN (
                    SELECT id FROM المشاريع_المراحل WHERE معرف_المشروع = %s
                ) AND حالة_مبلغ_المقاول = 'غير مدرج'
            """, (self.project_id,))

            tasks = cursor.fetchall()

            if not tasks:
                QMessageBox.information(self, "معلومات", "جميع أرصدة المقاولين تم إدراجها مسبقاً")
                conn.close()
                return

            total_amount = sum(task[2] for task in tasks)

            # تأكيد العملية
            reply = QMessageBox.question(
                self, "تأكيد الإدراج",
                f"هل تريد إدراج إجمالي مبلغ {total_amount:,.2f} لأرصدة {len(tasks)} مقاول؟",
                QMessageBox.Yes | QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                # تحديث حالة جميع المهام
                for task in tasks:
                    task_id, contractor_id, amount = task

                    # تحديث حالة المبلغ
                    cursor.execute("""
                        UPDATE المقاولات_مهام_المقاولين
                        SET حالة_مبلغ_المقاول = 'تم الإدراج'
                        WHERE id = %s
                    """, (task_id,))

                    # إضافة المبلغ إلى رصيد المقاول
                    cursor.execute("""
                        UPDATE المقاولين
                        SET الرصيد = الرصيد + %s
                        WHERE id = %s
                    """, (amount, contractor_id))

                conn.commit()
                conn.close()

                QMessageBox.information(self, "نجح", f"تم إدراج إجمالي مبلغ {total_amount:,.2f} لأرصدة {len(tasks)} مقاول بنجاح")
                self.load_contractors_data()
            else:
                conn.close()

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في إدراج الأرصدة: {str(e)}")

    def filter_contractors_by_name(self):
        """فلترة المقاولين حسب اسم المقاول"""
        selected_contractor = self.contractors_filter_combo.currentText()

        for row in range(self.contractors_table.rowCount()):
            if selected_contractor == "جميع المقاولين":
                self.contractors_table.setRowHidden(row, False)
            else:
                contractor_name_item = self.contractors_table.item(row, 2)  # عمود اسم المقاول
                if contractor_name_item:
                    show_row = selected_contractor in contractor_name_item.text()
                    self.contractors_table.setRowHidden(row, not show_row)
                else:
                    self.contractors_table.setRowHidden(row, True)

    # دوال تاب العمال
    def insert_worker_balance(self):
        """إدراج رصيد لعامل محدد"""
        current_row = self.workers_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد عامل لإدراج الرصيد")
            return

        try:
            # الحصول على معرف العامل والمبلغ
            worker_id_item = self.workers_table.item(current_row, 0)
            amount_item = self.workers_table.item(current_row, 5)  # عمود مبلغ العامل
            status_item = self.workers_table.item(current_row, 6)  # عمود حالة المبلغ

            if not worker_id_item or not amount_item:
                QMessageBox.warning(self, "خطأ", "لا يمكن الحصول على بيانات العامل")
                return

            worker_id = worker_id_item.text()
            amount = float(amount_item.text())
            current_status = status_item.text() if status_item else "غير مدرج"

            if current_status == "تم الإدراج":
                QMessageBox.information(self, "معلومات", "تم إدراج رصيد هذا العامل مسبقاً")
                return

            # تأكيد العملية
            reply = QMessageBox.question(
                self, "تأكيد الإدراج",
                f"هل تريد إدراج مبلغ {amount:,.2f} إلى رصيد العامل؟",
                QMessageBox.Yes | QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                conn = mysql.connector.connect(
                    host=host, user=user_r, password=password_r,
                    database="project_manager_V2"
                )
                cursor = conn.cursor()

                # تحديث حالة المبلغ في جدول مهام العمال
                cursor.execute("""
                    UPDATE المقاولات_مهام_العمال
                    SET حالة_مبلغ_العامل = 'تم الإدراج'
                    WHERE id = %s
                """, (worker_id,))

                # إضافة المبلغ إلى رصيد العامل
                cursor.execute("""
                    UPDATE العمال
                    SET الرصيد = الرصيد + %s
                    WHERE id = (
                        SELECT معرف_العامل FROM المقاولات_مهام_العمال WHERE id = %s
                    )
                """, (amount, worker_id))

                conn.commit()
                conn.close()

                QMessageBox.information(self, "نجح", f"تم إدراج المبلغ {amount:,.2f} إلى رصيد العامل بنجاح")
                self.load_workers_data()

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في إدراج الرصيد: {str(e)}")

    def insert_all_worker_balances(self):
        """إدراج الأرصدة لجميع العمال"""
        try:
            conn = mysql.connector.connect(
                host=host, user=user_r, password=password_r,
                database="project_manager_V2"
            )
            cursor = conn.cursor()

            # جلب مهام العمال غير المدرجة
            cursor.execute("""
                SELECT id, معرف_العامل, مبلغ_العامل
                FROM المقاولات_مهام_العمال
                WHERE معرف_المرحلة IN (
                    SELECT id FROM المشاريع_المراحل WHERE معرف_المشروع = %s
                ) AND حالة_مبلغ_العامل = 'غير مدرج'
            """, (self.project_id,))

            tasks = cursor.fetchall()

            if not tasks:
                QMessageBox.information(self, "معلومات", "جميع أرصدة العمال تم إدراجها مسبقاً")
                conn.close()
                return

            total_amount = sum(task[2] for task in tasks)

            # تأكيد العملية
            reply = QMessageBox.question(
                self, "تأكيد الإدراج",
                f"هل تريد إدراج إجمالي مبلغ {total_amount:,.2f} لأرصدة {len(tasks)} عامل؟",
                QMessageBox.Yes | QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                # تحديث حالة جميع المهام
                for task in tasks:
                    task_id, worker_id, amount = task

                    # تحديث حالة المبلغ
                    cursor.execute("""
                        UPDATE المقاولات_مهام_العمال
                        SET حالة_مبلغ_العامل = 'تم الإدراج'
                        WHERE id = %s
                    """, (task_id,))

                    # إضافة المبلغ إلى رصيد العامل
                    cursor.execute("""
                        UPDATE العمال
                        SET الرصيد = الرصيد + %s
                        WHERE id = %s
                    """, (amount, worker_id))

                conn.commit()
                conn.close()

                QMessageBox.information(self, "نجح", f"تم إدراج إجمالي مبلغ {total_amount:,.2f} لأرصدة {len(tasks)} عامل بنجاح")
                self.load_workers_data()
            else:
                conn.close()

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في إدراج الأرصدة: {str(e)}")

    def filter_workers_by_name(self):
        """فلترة العمال حسب اسم العامل"""
        selected_worker = self.workers_filter_combo.currentText()

        for row in range(self.workers_table.rowCount()):
            if selected_worker == "جميع العمال":
                self.workers_table.setRowHidden(row, False)
            else:
                worker_name_item = self.workers_table.item(row, 2)  # عمود اسم العامل
                if worker_name_item:
                    show_row = selected_worker in worker_name_item.text()
                    self.workers_table.setRowHidden(row, not show_row)
                else:
                    self.workers_table.setRowHidden(row, True)

    # دوال تاب الموردين
    def insert_supplier_balance(self):
        """إدراج رصيد لمورد محدد"""
        current_row = self.suppliers_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد مورد لإدراج الرصيد")
            return

        try:
            # الحصول على معرف المورد والمبلغ
            supplier_id_item = self.suppliers_table.item(current_row, 0)
            amount_item = self.suppliers_table.item(current_row, 5)  # عمود مبلغ المورد
            status_item = self.suppliers_table.item(current_row, 6)  # عمود حالة المبلغ

            if not supplier_id_item or not amount_item:
                QMessageBox.warning(self, "خطأ", "لا يمكن الحصول على بيانات المورد")
                return

            supplier_id = supplier_id_item.text()
            amount = float(amount_item.text())
            current_status = status_item.text() if status_item else "غير مدرج"

            if current_status == "تم الإدراج":
                QMessageBox.information(self, "معلومات", "تم إدراج رصيد هذا المورد مسبقاً")
                return

            # تأكيد العملية
            reply = QMessageBox.question(
                self, "تأكيد الإدراج",
                f"هل تريد إدراج مبلغ {amount:,.2f} إلى رصيد المورد؟",
                QMessageBox.Yes | QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                conn = mysql.connector.connect(
                    host=host, user=user_r, password=password_r,
                    database="project_manager_V2"
                )
                cursor = conn.cursor()

                # تحديث حالة المبلغ في جدول مهام الموردين
                cursor.execute("""
                    UPDATE المقاولات_مهام_الموردين
                    SET حالة_مبلغ_المورد = 'تم الإدراج'
                    WHERE id = %s
                """, (supplier_id,))

                # إضافة المبلغ إلى رصيد المورد
                cursor.execute("""
                    UPDATE الموردين
                    SET الرصيد = الرصيد + %s
                    WHERE id = (
                        SELECT معرف_المورد FROM المقاولات_مهام_الموردين WHERE id = %s
                    )
                """, (amount, supplier_id))

                conn.commit()
                conn.close()

                QMessageBox.information(self, "نجح", f"تم إدراج المبلغ {amount:,.2f} إلى رصيد المورد بنجاح")
                self.load_suppliers_data()

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في إدراج الرصيد: {str(e)}")

    def insert_all_supplier_balances(self):
        """إدراج الأرصدة لجميع الموردين"""
        try:
            conn = mysql.connector.connect(
                host=host, user=user_r, password=password_r,
                database="project_manager_V2"
            )
            cursor = conn.cursor()

            # جلب مهام الموردين غير المدرجة
            cursor.execute("""
                SELECT id, معرف_المورد, مبلغ_المورد
                FROM المقاولات_مهام_الموردين
                WHERE معرف_المرحلة IN (
                    SELECT id FROM المشاريع_المراحل WHERE معرف_المشروع = %s
                ) AND حالة_مبلغ_المورد = 'غير مدرج'
            """, (self.project_id,))

            tasks = cursor.fetchall()

            if not tasks:
                QMessageBox.information(self, "معلومات", "جميع أرصدة الموردين تم إدراجها مسبقاً")
                conn.close()
                return

            total_amount = sum(task[2] for task in tasks)

            # تأكيد العملية
            reply = QMessageBox.question(
                self, "تأكيد الإدراج",
                f"هل تريد إدراج إجمالي مبلغ {total_amount:,.2f} لأرصدة {len(tasks)} مورد؟",
                QMessageBox.Yes | QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                # تحديث حالة جميع المهام
                for task in tasks:
                    task_id, supplier_id, amount = task

                    # تحديث حالة المبلغ
                    cursor.execute("""
                        UPDATE المقاولات_مهام_الموردين
                        SET حالة_مبلغ_المورد = 'تم الإدراج'
                        WHERE id = %s
                    """, (task_id,))

                    # إضافة المبلغ إلى رصيد المورد
                    cursor.execute("""
                        UPDATE الموردين
                        SET الرصيد = الرصيد + %s
                        WHERE id = %s
                    """, (amount, supplier_id))

                conn.commit()
                conn.close()

                QMessageBox.information(self, "نجح", f"تم إدراج إجمالي مبلغ {total_amount:,.2f} لأرصدة {len(tasks)} مورد بنجاح")
                self.load_suppliers_data()
            else:
                conn.close()

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في إدراج الأرصدة: {str(e)}")

    def filter_suppliers_by_name(self):
        """فلترة الموردين حسب اسم المورد"""
        selected_supplier = self.suppliers_filter_combo.currentText()

        for row in range(self.suppliers_table.rowCount()):
            if selected_supplier == "جميع الموردين":
                self.suppliers_table.setRowHidden(row, False)
            else:
                supplier_name_item = self.suppliers_table.item(row, 2)  # عمود اسم المورد
                if supplier_name_item:
                    show_row = selected_supplier in supplier_name_item.text()
                    self.suppliers_table.setRowHidden(row, not show_row)
                else:
                    self.suppliers_table.setRowHidden(row, True)

    # دوال تاب الجدول الزمني
    def manage_timeline_status(self):
        """إدارة حالة المهام في الجدول الزمني"""
        current_row = self.timeline_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد مهمة لإدارة حالتها")
            return

        # قائمة الحالات المتاحة
        statuses = ["لم يبدأ", "قيد التنفيذ", "منتهي", "متوقف"]

        current_status_item = self.timeline_table.item(current_row, 7)  # عمود الحالة
        current_status = current_status_item.text() if current_status_item else "لم يبدأ"

        # حوار اختيار الحالة الجديدة
        new_status, ok = QInputDialog.getItem(
            self, "تغيير الحالة",
            f"الحالة الحالية: {current_status}\nاختر الحالة الجديدة:",
            statuses, 0, False
        )

        if ok and new_status != current_status:
            try:
                # الحصول على معرف المهمة
                task_id_item = self.timeline_table.item(current_row, 0)
                if not task_id_item:
                    QMessageBox.warning(self, "خطأ", "لا يمكن الحصول على معرف المهمة")
                    return

                task_id = task_id_item.text()

                conn = mysql.connector.connect(
                    host=host, user=user_r, password=password_r,
                    database="project_manager_V2"
                )
                cursor = conn.cursor()

                # تحديث حالة المهمة
                cursor.execute("""
                    UPDATE المشاريع_مهام_المهندسين
                    SET الحالة = %s
                    WHERE id = %s
                """, (new_status, task_id))

                conn.commit()
                conn.close()

                QMessageBox.information(self, "نجح", f"تم تغيير الحالة إلى '{new_status}' بنجاح")
                self.load_timeline_data()

            except Exception as e:
                QMessageBox.warning(self, "خطأ", f"فشل في تغيير الحالة: {str(e)}")

    def filter_timeline_by_status(self):
        """فلترة الجدول الزمني حسب الحالة"""
        selected_status = self.timeline_status_filter_combo.currentText()

        for row in range(self.timeline_table.rowCount()):
            if selected_status == "جميع الحالات":
                self.timeline_table.setRowHidden(row, False)
            else:
                status_item = self.timeline_table.item(row, 7)  # عمود الحالة
                if status_item:
                    show_row = selected_status == status_item.text()
                    self.timeline_table.setRowHidden(row, not show_row)
                else:
                    self.timeline_table.setRowHidden(row, True)

    # دوال تاب المصروفات
    def filter_expenses_by_custody(self):
        """فلترة المصروفات حسب رقم العهدة"""
        selected_custody = self.expenses_custody_filter_combo.currentText()

        for row in range(self.expenses_table.rowCount()):
            if selected_custody == "جميع العهد":
                self.expenses_table.setRowHidden(row, False)
            else:
                # البحث في عمود رقم العهدة (يجب تحديد العمود المناسب)
                custody_item = self.expenses_table.item(row, 3)  # افتراض أن رقم العهدة في العمود الرابع
                if custody_item:
                    show_row = selected_custody in custody_item.text()
                    self.expenses_table.setRowHidden(row, not show_row)
                else:
                    self.expenses_table.setRowHidden(row, True)

    # دوال تاب العهد المالية
    def transfer_custody(self):
        """ترحيل العهدة"""
        current_row = self.custody_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد عهدة للترحيل")
            return

        QMessageBox.information(self, "ترحيل العهدة", "سيتم فتح نافذة ترحيل العهدة")

    def close_custody(self):
        """إغلاق العهدة"""
        current_row = self.custody_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد عهدة للإغلاق")
            return

        # تأكيد الإغلاق
        reply = QMessageBox.question(
            self, "تأكيد الإغلاق",
            "هل أنت متأكد من إغلاق هذه العهدة؟\nلن يمكن التعديل عليها بعد الإغلاق.",
            QMessageBox.Yes | QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                custody_id_item = self.custody_table.item(current_row, 0)
                if not custody_id_item:
                    QMessageBox.warning(self, "خطأ", "لا يمكن الحصول على معرف العهدة")
                    return

                custody_id = custody_id_item.text()

                conn = mysql.connector.connect(
                    host=host, user=user_r, password=password_r,
                    database="project_manager_V2"
                )
                cursor = conn.cursor()

                # إغلاق العهدة
                cursor.execute("""
                    UPDATE المقاولات_العهد
                    SET حالة_العهدة = 'مغلقة', تاريخ_الإغلاق = CURDATE()
                    WHERE id = %s
                """, (custody_id,))

                conn.commit()
                conn.close()

                QMessageBox.information(self, "نجح", "تم إغلاق العهدة بنجاح")
                self.load_custody_data()

            except Exception as e:
                QMessageBox.warning(self, "خطأ", f"فشل في إغلاق العهدة: {str(e)}")

    def filter_custody_by_number(self):
        """فلترة العهد حسب رقم العهدة"""
        selected_custody = self.custody_filter_combo.currentText()

        for row in range(self.custody_table.rowCount()):
            if selected_custody == "جميع العهد":
                self.custody_table.setRowHidden(row, False)
            else:
                custody_number_item = self.custody_table.item(row, 2)  # عمود رقم العهدة
                if custody_number_item:
                    show_row = selected_custody in custody_number_item.text()
                    self.custody_table.setRowHidden(row, not show_row)
                else:
                    self.custody_table.setRowHidden(row, True)

    # دوال تحميل البيانات للفلاتر
    def load_filter_data(self):
        """تحميل بيانات الفلاتر من قاعدة البيانات"""
        try:
            conn = mysql.connector.connect(
                host=host, user=user_r, password=password_r,
                database="project_manager_V2"
            )
            cursor = conn.cursor()

            # تحميل أسماء المراحل للفلتر
            if hasattr(self, 'phases_filter_combo'):
                cursor.execute("""
                    SELECT DISTINCT اسم_المرحلة FROM المشاريع_المراحل
                    WHERE معرف_المشروع = %s AND اسم_المرحلة IS NOT NULL
                """, (self.project_id,))
                phases = cursor.fetchall()
                self.phases_filter_combo.clear()
                self.phases_filter_combo.addItem("جميع المراحل")
                for phase in phases:
                    self.phases_filter_combo.addItem(phase[0])

            # تحميل أسماء المهندسين للفلتر
            if hasattr(self, 'engineers_filter_combo'):
                cursor.execute("""
                    SELECT DISTINCT م.اسم_الموظف
                    FROM الموظفين م
                    JOIN المشاريع_مهام_المهندسين مم ON م.id = مم.معرف_المهندس
                    JOIN المشاريع_المراحل مر ON مم.معرف_المرحلة = مر.id
                    WHERE مر.معرف_المشروع = %s
                """, (self.project_id,))
                engineers = cursor.fetchall()
                self.engineers_filter_combo.clear()
                self.engineers_filter_combo.addItem("جميع المهندسين")
                for engineer in engineers:
                    self.engineers_filter_combo.addItem(engineer[0])

            # تحميل أرقام العهد للفلاتر
            if hasattr(self, 'expenses_custody_filter_combo'):
                cursor.execute("""
                    SELECT DISTINCT رقم_العهدة FROM المقاولات_العهد
                    WHERE معرف_المشروع = %s AND رقم_العهدة IS NOT NULL
                """, (self.project_id,))
                custodies = cursor.fetchall()
                self.expenses_custody_filter_combo.clear()
                self.expenses_custody_filter_combo.addItem("جميع العهد")
                for custody in custodies:
                    self.expenses_custody_filter_combo.addItem(custody[0])

                if hasattr(self, 'custody_filter_combo'):
                    self.custody_filter_combo.clear()
                    self.custody_filter_combo.addItem("جميع العهد")
                    for custody in custodies:
                        self.custody_filter_combo.addItem(custody[0])

            # تحميل أسماء المقاولين للفلتر
            if hasattr(self, 'contractors_filter_combo'):
                cursor.execute("""
                    SELECT DISTINCT م.اسم_المقاول
                    FROM المقاولين م
                    JOIN المقاولات_مهام_المقاولين مم ON م.id = مم.معرف_المقاول
                    JOIN المشاريع_المراحل مر ON مم.معرف_المرحلة = مر.id
                    WHERE مر.معرف_المشروع = %s
                """, (self.project_id,))
                contractors = cursor.fetchall()
                self.contractors_filter_combo.clear()
                self.contractors_filter_combo.addItem("جميع المقاولين")
                for contractor in contractors:
                    self.contractors_filter_combo.addItem(contractor[0])

            # تحميل أسماء العمال للفلتر
            if hasattr(self, 'workers_filter_combo'):
                cursor.execute("""
                    SELECT DISTINCT ع.اسم_العامل
                    FROM العمال ع
                    JOIN المقاولات_مهام_العمال مع ON ع.id = مع.معرف_العامل
                    JOIN المشاريع_المراحل مر ON مع.معرف_المرحلة = مر.id
                    WHERE مر.معرف_المشروع = %s
                """, (self.project_id,))
                workers = cursor.fetchall()
                self.workers_filter_combo.clear()
                self.workers_filter_combo.addItem("جميع العمال")
                for worker in workers:
                    self.workers_filter_combo.addItem(worker[0])

            # تحميل أسماء الموردين للفلتر
            if hasattr(self, 'suppliers_filter_combo'):
                cursor.execute("""
                    SELECT DISTINCT مو.اسم_المورد
                    FROM الموردين مو
                    JOIN المقاولات_مهام_الموردين مم ON مو.id = مم.معرف_المورد
                    JOIN المشاريع_المراحل مر ON مم.معرف_المرحلة = مر.id
                    WHERE مر.معرف_المشروع = %s
                """, (self.project_id,))
                suppliers = cursor.fetchall()
                self.suppliers_filter_combo.clear()
                self.suppliers_filter_combo.addItem("جميع الموردين")
                for supplier in suppliers:
                    self.suppliers_filter_combo.addItem(supplier[0])

            conn.close()

        except Exception as e:
            print(f"خطأ في تحميل بيانات الفلاتر: {e}")


class EngineerTaskDialog(QDialog):
    """حوار إضافة/تعديل مهمة مهندس"""

    def __init__(self, parent=None, project_id=None, task_id=None):
        super().__init__(parent)
        self.project_id = project_id
        self.task_id = task_id
        self.is_edit_mode = task_id is not None
        self._engineer_changed_manually = False  # علامة لتتبع تغيير المهندس يدوياً

        self.setup_dialog()
        self.create_ui()

        if self.is_edit_mode:
            self.load_task_data()
        else:
            # في وضع الإضافة، تفعيل التعبئة التلقائية من البداية
            self._engineer_changed_manually = True

        apply_stylesheet(self)

    def setup_dialog(self):
        """إعداد الحوار"""
        title = "تعديل مهمة المهندس" if self.is_edit_mode else "إضافة مهمة مهندس جديدة"
        self.setWindowTitle(title)
        self.setGeometry(200, 200, 600, 500)
        self.setLayoutDirection(Qt.RightToLeft)
        self.setModal(True)

    def create_ui(self):
        """إنشاء واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setSpacing(20)

        # معلومات المهمة
        form_group = QGroupBox("معلومات المهمة")
        form_layout = QGridLayout(form_group)
        form_layout.setSpacing(15)

        # اختيار المرحلة
        form_layout.addWidget(QLabel("المرحلة:"), 0, 0)
        self.phase_combo = QComboBox()
        self.load_phases()
        form_layout.addWidget(self.phase_combo, 0, 1)

        # اختيار المهندس
        form_layout.addWidget(QLabel("المهندس:"), 1, 0)
        self.engineer_combo = QComboBox()
        self.load_engineers()
        form_layout.addWidget(self.engineer_combo, 1, 1)

        # نسبة المهندس
        form_layout.addWidget(QLabel("نسبة المهندس (%):"), 2, 0)
        self.percentage_spin = QSpinBox()
        self.percentage_spin.setRange(0, 100)
        self.percentage_spin.setValue(0)
        self.percentage_spin.valueChanged.connect(self.calculate_amount)
        form_layout.addWidget(self.percentage_spin, 2, 1)

        # مبلغ المهندس
        form_layout.addWidget(QLabel("مبلغ المهندس:"), 3, 0)
        self.amount_spin = QDoubleSpinBox()
        self.amount_spin.setRange(0, 999999.99)
        self.amount_spin.setDecimals(2)
        self.amount_spin.setValue(0.0)
        form_layout.addWidget(self.amount_spin, 3, 1)

        # حالة المبلغ
        form_layout.addWidget(QLabel("حالة المبلغ:"), 4, 0)
        self.amount_status_combo = QComboBox()
        self.amount_status_combo.addItems(["غير مدرج", "تم الإدراج"])
        form_layout.addWidget(self.amount_status_combo, 4, 1)

        # تاريخ البدء
        form_layout.addWidget(QLabel("تاريخ البدء:"), 5, 0)
        self.start_date = QDateEdit()
        self.start_date.setDate(QDate.currentDate())
        self.start_date.setCalendarPopup(True)
        self.start_date.dateChanged.connect(self.validate_dates)
        form_layout.addWidget(self.start_date, 5, 1)

        # تاريخ الانتهاء
        form_layout.addWidget(QLabel("تاريخ الانتهاء:"), 6, 0)
        self.end_date = QDateEdit()
        self.end_date.setDate(QDate.currentDate().addDays(30))
        self.end_date.setCalendarPopup(True)
        self.end_date.dateChanged.connect(self.validate_dates)
        form_layout.addWidget(self.end_date, 6, 1)

        # حالة المهمة
        form_layout.addWidget(QLabel("حالة المهمة:"), 7, 0)
        self.task_status_combo = QComboBox()
        self.task_status_combo.addItems(["لم يبدأ", "قيد التنفيذ", "منتهي", "متوقف"])
        form_layout.addWidget(self.task_status_combo, 7, 1)

        # ملاحظات
        form_layout.addWidget(QLabel("ملاحظات:"), 8, 0)
        self.notes_edit = QTextEdit()
        self.notes_edit.setMaximumHeight(100)
        form_layout.addWidget(self.notes_edit, 8, 1)

        layout.addWidget(form_group)

        # أزرار الحفظ والإلغاء
        buttons_layout = QHBoxLayout()

        save_btn = QPushButton("حفظ")
        save_btn.setIcon(qta.icon('fa5s.save'))
        save_btn.clicked.connect(self.save_task)
        save_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #2ecc71;
            }
        """)

        cancel_btn = QPushButton("إلغاء")
        cancel_btn.setIcon(qta.icon('fa5s.times'))
        cancel_btn.clicked.connect(self.reject)
        cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #ec7063;
            }
        """)

        buttons_layout.addStretch()
        buttons_layout.addWidget(save_btn)
        buttons_layout.addWidget(cancel_btn)

        layout.addLayout(buttons_layout)

    def load_phases(self):
        """تحميل المراحل المتاحة"""
        try:
            if not self.project_id:
                return

            conn = mysql.connector.connect(
                host=host, user=user_r, password=password_r,
                database="project_manager_V2"
            )
            cursor = conn.cursor()

            cursor.execute("""
                SELECT id, اسم_المرحلة, وصف_المرحلة FROM المشاريع_المراحل
                WHERE معرف_المشروع = %s
                ORDER BY اسم_المرحلة
            """, (self.project_id,))

            phases = cursor.fetchall()
            self.phase_combo.clear()

            for phase_id, phase_name, phase_description in phases:
                # تنسيق العرض: اسم المرحلة - وصف المرحلة
                if phase_description and phase_description.strip():
                    display_text = f"{phase_name} - {phase_description}"
                else:
                    display_text = phase_name
                self.phase_combo.addItem(display_text, phase_id)

            conn.close()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل المراحل: {str(e)}")

    def load_engineers(self):
        """تحميل المهندسين المتاحين"""
        try:
            conn = mysql.connector.connect(
                host=host, user=user_r, password=password_r,
                database="project_manager_V2"
            )
            cursor = conn.cursor()

            cursor.execute("""
                SELECT id, اسم_الموظف, الوظيفة, النسبة FROM الموظفين
                WHERE الحالة = 'نشط'
                AND (التصنيف = 'مهندس' OR الوظيفة LIKE '%مهندس%')
                AND الوظيفة NOT LIKE '%استقبال%'
                AND الوظيفة NOT LIKE '%موظف%'
                AND الوظيفة NOT LIKE '%عامل%'
                ORDER BY اسم_الموظف
            """)

            engineers = cursor.fetchall()
            self.engineer_combo.clear()

            for engineer_id, engineer_name, job_title, default_percentage in engineers:
                # تنسيق العرض: اسم المهندس - الوظيفة
                display_text = f"{engineer_name} - {job_title}" if job_title else engineer_name
                self.engineer_combo.addItem(display_text, engineer_id)

                # حفظ النسبة الافتراضية كبيانات إضافية
                self.engineer_combo.setItemData(self.engineer_combo.count() - 1,
                                              {'id': engineer_id, 'default_percentage': default_percentage or 0},
                                              Qt.UserRole + 1)

            # ربط إشارة تغيير الاختيار بدالة التعبئة التلقائية
            self.engineer_combo.currentIndexChanged.connect(self.on_engineer_changed)

            conn.close()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل المهندسين: {str(e)}")

    def on_engineer_changed(self):
        """معالج تغيير اختيار المهندس - تعبئة النسبة الافتراضية"""
        try:
            # الحصول على البيانات الإضافية للمهندس المحدد
            current_data = self.engineer_combo.currentData(Qt.UserRole + 1)

            if current_data and isinstance(current_data, dict):
                default_percentage = current_data.get('default_percentage', 0)

                # تعبئة النسبة الافتراضية فقط إذا كانت أكبر من صفر
                if default_percentage > 0:
                    # في وضع الإضافة، أو في وضع التعديل عند تغيير المهندس
                    if not self.is_edit_mode or self._engineer_changed_manually:
                        self.percentage_spin.setValue(int(default_percentage))
                        # تحديث المبلغ بناءً على النسبة الجديدة
                        self.calculate_amount()

        except Exception as e:
            print(f"خطأ في تعبئة النسبة الافتراضية: {e}")

    def get_engineer_default_percentage(self, engineer_id):
        """جلب النسبة الافتراضية للمهندس من قاعدة البيانات"""
        try:
            if not engineer_id:
                return 0

            conn = mysql.connector.connect(
                host=host, user=user_r, password=password_r,
                database="project_manager_V2"
            )
            cursor = conn.cursor()

            cursor.execute("SELECT النسبة FROM الموظفين WHERE id = %s", (engineer_id,))
            result = cursor.fetchone()

            conn.close()

            return result[0] if result and result[0] else 0

        except Exception as e:
            print(f"خطأ في جلب النسبة الافتراضية: {e}")
            return 0

    def calculate_amount(self):
        """حساب مبلغ المهندس بناءً على النسبة"""
        try:
            phase_id = self.phase_combo.currentData()
            if not phase_id:
                return

            conn = mysql.connector.connect(
                host=host, user=user_r, password=password_r,
                database="project_manager_V2"
            )
            cursor = conn.cursor()

            cursor.execute("SELECT الإجمالي FROM المشاريع_المراحل WHERE id = %s", (phase_id,))
            result = cursor.fetchone()

            if result:
                phase_total = float(result[0]) if result[0] else 0.0
                percentage = self.percentage_spin.value()
                amount = (phase_total * percentage) / 100
                self.amount_spin.setValue(amount)

            conn.close()

        except Exception as e:
            print(f"خطأ في حساب المبلغ: {e}")

    def validate_dates(self):
        """التحقق من صحة التواريخ"""
        if self.start_date.date() > self.end_date.date():
            # تعيين تاريخ الانتهاء ليكون بعد تاريخ البدء بـ 30 يوم
            self.end_date.setDate(self.start_date.date().addDays(30))
            QMessageBox.warning(self, "تحذير", "تم تعديل تاريخ الانتهاء ليكون بعد تاريخ البدء")

    def load_task_data(self):
        """تحميل بيانات المهمة للتعديل"""
        try:
            conn = mysql.connector.connect(
                host=host, user=user_r, password=password_r,
                database="project_manager_V2"
            )
            cursor = conn.cursor()

            cursor.execute("""
                SELECT معرف_المرحلة, معرف_المهندس, نسبة_المهندس, مبلغ_المهندس,
                       حالة_مبلغ_المهندس, تاريخ_البداية, تاريخ_النهاية, الحالة, ملاحظات
                FROM المشاريع_مهام_المهندسين
                WHERE id = %s
            """, (self.task_id,))

            result = cursor.fetchone()
            conn.close()

            if result:
                phase_id, engineer_id, percentage, amount, amount_status, start_date, end_date, task_status, notes = result

                # تعيين المرحلة
                for i in range(self.phase_combo.count()):
                    if self.phase_combo.itemData(i) == phase_id:
                        self.phase_combo.setCurrentIndex(i)
                        break

                # تعيين المهندس (بدون تشغيل التعبئة التلقائية)
                self._engineer_changed_manually = False  # تعطيل التعبئة التلقائية مؤقتاً
                for i in range(self.engineer_combo.count()):
                    if self.engineer_combo.itemData(i) == engineer_id:
                        self.engineer_combo.setCurrentIndex(i)
                        break

                # تعيين باقي البيانات
                self.percentage_spin.setValue(percentage or 0)
                self.amount_spin.setValue(float(amount) if amount else 0.0)
                self.amount_status_combo.setCurrentText(amount_status or "غير مدرج")

                # تعيين التواريخ
                if start_date:
                    self.start_date.setDate(QDate.fromString(str(start_date), "yyyy-MM-dd"))
                if end_date:
                    self.end_date.setDate(QDate.fromString(str(end_date), "yyyy-MM-dd"))

                # تعيين حالة المهمة والملاحظات
                self.task_status_combo.setCurrentText(task_status or "لم يبدأ")
                self.notes_edit.setPlainText(notes or "")

                # تفعيل التعبئة التلقائية للتغييرات المستقبلية
                self._engineer_changed_manually = True

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل بيانات المهمة: {str(e)}")

    def save_task(self):
        """حفظ بيانات المهمة"""
        # التحقق من صحة البيانات
        phase_id = self.phase_combo.currentData()
        engineer_id = self.engineer_combo.currentData()

        if not phase_id:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار المرحلة")
            return

        if not engineer_id:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار المهندس")
            return

        percentage = self.percentage_spin.value()
        amount = self.amount_spin.value()
        amount_status = self.amount_status_combo.currentText()
        start_date = self.start_date.date().toString("yyyy-MM-dd")
        end_date = self.end_date.date().toString("yyyy-MM-dd")
        task_status = self.task_status_combo.currentText()
        notes = self.notes_edit.toPlainText().strip()

        # التحقق من صحة التواريخ
        if self.start_date.date() > self.end_date.date():
            QMessageBox.warning(self, "تحذير", "تاريخ البدء يجب أن يكون قبل تاريخ الانتهاء")
            return

        try:
            conn = mysql.connector.connect(
                host=host, user=user_r, password=password_r,
                database="project_manager_V2"
            )
            cursor = conn.cursor()

            if self.is_edit_mode:
                # تحديث المهمة الموجودة
                cursor.execute("""
                    UPDATE المشاريع_مهام_المهندسين
                    SET معرف_المرحلة = %s, معرف_المهندس = %s, نسبة_المهندس = %s,
                        مبلغ_المهندس = %s, حالة_مبلغ_المهندس = %s, تاريخ_البداية = %s,
                        تاريخ_النهاية = %s, الحالة = %s, ملاحظات = %s
                    WHERE id = %s
                """, (phase_id, engineer_id, percentage, amount, amount_status, start_date,
                      end_date, task_status, notes, self.task_id))

                message = "تم تحديث مهمة المهندس بنجاح"
            else:
                # إضافة مهمة جديدة
                cursor.execute("""
                    INSERT INTO المشاريع_مهام_المهندسين
                    (معرف_المرحلة, معرف_المهندس, نسبة_المهندس, مبلغ_المهندس,
                     حالة_مبلغ_المهندس, تاريخ_البداية, تاريخ_النهاية, الحالة, ملاحظات, المستخدم)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """, (phase_id, engineer_id, percentage, amount, amount_status, start_date,
                      end_date, task_status, notes, "admin"))

                message = "تم إضافة مهمة المهندس بنجاح"

            conn.commit()
            conn.close()

            QMessageBox.information(self, "نجح", message)
            self.accept()

        except mysql.connector.IntegrityError:
            QMessageBox.warning(self, "تحذير", "هذا المهندس مُعيّن بالفعل لهذه المرحلة")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ المهمة: {str(e)}")


class TimelineEntryDialog(QDialog):
    """حوار إضافة/تعديل إدخال الجدول الزمني"""

    def __init__(self, parent=None, project_id=None, entry_id=None):
        super().__init__(parent)
        self.project_id = project_id
        self.entry_id = entry_id
        self.is_edit_mode = entry_id is not None

        self.setup_dialog()
        self.create_ui()

        if self.is_edit_mode:
            self.load_entry_data()

        apply_stylesheet(self)

    def setup_dialog(self):
        """إعداد الحوار"""
        title = "تعديل الجدولة الزمنية" if self.is_edit_mode else "إضافة جدولة زمنية جديدة"
        self.setWindowTitle(title)
        self.setGeometry(200, 200, 600, 450)
        self.setLayoutDirection(Qt.RightToLeft)
        self.setModal(True)

    def create_ui(self):
        """إنشاء واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setSpacing(20)

        # معلومات الجدولة
        form_group = QGroupBox("معلومات الجدولة الزمنية")
        form_layout = QGridLayout(form_group)
        form_layout.setSpacing(15)

        # اختيار المرحلة
        form_layout.addWidget(QLabel("المرحلة:"), 0, 0)
        self.phase_combo = QComboBox()
        self.load_phases()
        form_layout.addWidget(self.phase_combo, 0, 1)

        # اختيار المهندس
        form_layout.addWidget(QLabel("المهندس:"), 1, 0)
        self.engineer_combo = QComboBox()
        self.load_engineers()
        form_layout.addWidget(self.engineer_combo, 1, 1)

        # تاريخ البداية
        form_layout.addWidget(QLabel("تاريخ البداية:"), 2, 0)
        self.start_date = QDateEdit()
        self.start_date.setDate(QDate.currentDate())
        self.start_date.setCalendarPopup(True)
        form_layout.addWidget(self.start_date, 2, 1)

        # تاريخ النهاية
        form_layout.addWidget(QLabel("تاريخ النهاية:"), 3, 0)
        self.end_date = QDateEdit()
        self.end_date.setDate(QDate.currentDate().addDays(30))
        self.end_date.setCalendarPopup(True)
        form_layout.addWidget(self.end_date, 3, 1)

        # الحالة
        form_layout.addWidget(QLabel("الحالة:"), 4, 0)
        self.status_combo = QComboBox()
        self.status_combo.addItems(["لم يبدأ", "قيد التنفيذ", "منتهي", "متوقف"])
        form_layout.addWidget(self.status_combo, 4, 1)

        # ملاحظات
        form_layout.addWidget(QLabel("ملاحظات:"), 5, 0)
        self.notes_edit = QTextEdit()
        self.notes_edit.setMaximumHeight(100)
        form_layout.addWidget(self.notes_edit, 5, 1)

        layout.addWidget(form_group)

        # أزرار الحفظ والإلغاء
        buttons_layout = QHBoxLayout()

        save_btn = QPushButton("حفظ")
        save_btn.setIcon(qta.icon('fa5s.save'))
        save_btn.clicked.connect(self.save_entry)
        save_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #2ecc71;
            }
        """)

        cancel_btn = QPushButton("إلغاء")
        cancel_btn.setIcon(qta.icon('fa5s.times'))
        cancel_btn.clicked.connect(self.reject)
        cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #ec7063;
            }
        """)

        buttons_layout.addStretch()
        buttons_layout.addWidget(save_btn)
        buttons_layout.addWidget(cancel_btn)

        layout.addLayout(buttons_layout)

    def load_phases(self):
        """تحميل المراحل المتاحة"""
        try:
            if not self.project_id:
                return

            conn = mysql.connector.connect(
                host=host, user=user_r, password=password_r,
                database="project_manager_V2"
            )
            cursor = conn.cursor()

            cursor.execute("""
                SELECT id, اسم_المرحلة, وصف_المرحلة FROM المشاريع_المراحل
                WHERE معرف_المشروع = %s
                ORDER BY اسم_المرحلة
            """, (self.project_id,))

            phases = cursor.fetchall()
            self.phase_combo.clear()

            for phase_id, phase_name, phase_description in phases:
                # تنسيق العرض: اسم المرحلة - وصف المرحلة
                if phase_description and phase_description.strip():
                    display_text = f"{phase_name} - {phase_description}"
                else:
                    display_text = phase_name
                self.phase_combo.addItem(display_text, phase_id)

            conn.close()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل المراحل: {str(e)}")

    def load_engineers(self):
        """تحميل المهندسين المتاحين"""
        try:
            conn = mysql.connector.connect(
                host=host, user=user_r, password=password_r,
                database="project_manager_V2"
            )
            cursor = conn.cursor()

            cursor.execute("""
                SELECT id, اسم_الموظف, الوظيفة FROM الموظفين
                WHERE الحالة = 'نشط'
                AND (التصنيف = 'مهندس' OR الوظيفة LIKE '%مهندس%')
                AND الوظيفة NOT LIKE '%استقبال%'
                AND الوظيفة NOT LIKE '%موظف%'
                AND الوظيفة NOT LIKE '%عامل%'
                ORDER BY اسم_الموظف
            """)

            engineers = cursor.fetchall()
            self.engineer_combo.clear()

            for engineer_id, engineer_name, job_title in engineers:
                # تنسيق العرض: اسم المهندس - الوظيفة
                display_text = f"{engineer_name} - {job_title}" if job_title else engineer_name
                self.engineer_combo.addItem(display_text, engineer_id)

            conn.close()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل المهندسين: {str(e)}")

    def load_entry_data(self):
        """تحميل بيانات الإدخال للتعديل"""
        try:
            conn = mysql.connector.connect(
                host=host, user=user_r, password=password_r,
                database="project_manager_V2"
            )
            cursor = conn.cursor()

            cursor.execute("""
                SELECT معرف_المرحلة, معرف_المهندس, تاريخ_البداية, تاريخ_النهاية,
                       الحالة, ملاحظات
                FROM المشاريع_مهام_المهندسين
                WHERE id = %s
            """, (self.entry_id,))

            result = cursor.fetchone()
            conn.close()

            if result:
                phase_id, engineer_id, start_date, end_date, status, notes = result

                # تعيين المرحلة
                for i in range(self.phase_combo.count()):
                    if self.phase_combo.itemData(i) == phase_id:
                        self.phase_combo.setCurrentIndex(i)
                        break

                # تعيين المهندس
                for i in range(self.engineer_combo.count()):
                    if self.engineer_combo.itemData(i) == engineer_id:
                        self.engineer_combo.setCurrentIndex(i)
                        break

                # تعيين التواريخ
                if start_date:
                    self.start_date.setDate(QDate.fromString(str(start_date), "yyyy-MM-dd"))
                if end_date:
                    self.end_date.setDate(QDate.fromString(str(end_date), "yyyy-MM-dd"))

                # تعيين الحالة والملاحظات
                self.status_combo.setCurrentText(status or "لم يبدأ")
                self.notes_edit.setPlainText(notes or "")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل بيانات الجدولة: {str(e)}")

    def save_entry(self):
        """حفظ بيانات الجدولة"""
        # التحقق من صحة البيانات
        phase_id = self.phase_combo.currentData()
        engineer_id = self.engineer_combo.currentData()

        if not phase_id:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار المرحلة")
            return

        if not engineer_id:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار المهندس")
            return

        start_date = self.start_date.date().toString("yyyy-MM-dd")
        end_date = self.end_date.date().toString("yyyy-MM-dd")
        status = self.status_combo.currentText()
        notes = self.notes_edit.toPlainText().strip()

        # التحقق من صحة التواريخ
        if self.start_date.date() > self.end_date.date():
            QMessageBox.warning(self, "تحذير", "تاريخ البداية يجب أن يكون قبل تاريخ النهاية")
            return

        try:
            conn = mysql.connector.connect(
                host=host, user=user_r, password=password_r,
                database="project_manager_V2"
            )
            cursor = conn.cursor()

            if self.is_edit_mode:
                # تحديث الجدولة الموجودة
                cursor.execute("""
                    UPDATE المشاريع_مهام_المهندسين
                    SET معرف_المرحلة = %s, معرف_المهندس = %s, تاريخ_البداية = %s,
                        تاريخ_النهاية = %s, الحالة = %s, ملاحظات = %s
                    WHERE id = %s
                """, (phase_id, engineer_id, start_date, end_date, status, notes, self.entry_id))

                message = "تم تحديث الجدولة الزمنية بنجاح"
            else:
                # إضافة جدولة جديدة
                cursor.execute("""
                    INSERT INTO المشاريع_مهام_المهندسين
                    (معرف_المرحلة, معرف_المهندس, تاريخ_البداية, تاريخ_النهاية,
                     الحالة, ملاحظات, المستخدم)
                    VALUES (%s, %s, %s, %s, %s, %s, %s)
                """, (phase_id, engineer_id, start_date, end_date, status, notes, "admin"))

                message = "تم إضافة الجدولة الزمنية بنجاح"

            conn.commit()
            conn.close()

            QMessageBox.information(self, "نجح", message)
            self.accept()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ الجدولة: {str(e)}")


class PhaseDialog(QDialog):
    """حوار إضافة/تعديل مرحلة المشروع"""

    def __init__(self, parent=None, project_id=None, phase_id=None, project_type="تصميم"):
        super().__init__(parent)
        self.project_id = project_id
        self.phase_id = phase_id
        self.project_type = project_type
        self.is_edit_mode = phase_id is not None

        self.setup_dialog()
        self.create_ui()

        if self.is_edit_mode:
            self.load_phase_data()

        apply_stylesheet(self)

    def get_phases_list_by_type(self):
        """إرجاع قائمة المراحل حسب نوع المشروع"""
        if self.project_type == "مقاولات":
            return [
                # مراحل التأسيس
                "حفر الأساسات", "صب الخرسانة المسلحة", "أعمال العزل", "ردم وتسوية",
                # مراحل التنفيذ
                "بناء الجدران", "أعمال السقف", "تمديدات الكهرباء", "تمديدات السباكة", "أعمال التكييف",
                # مراحل التشطيب
                "أعمال البلاط", "أعمال الدهان", "تركيب الأبواب والنوافذ", "أعمال الديكور", "تنسيق الحدائق"
            ]
        else:  # للتصميم والأنواع الأخرى
            return [
                "الرفع المساحي", "مقترح مبدئي 2D", "تصميم 2D", "تصميم 3D",
                "تصميم إنشائي", "خرائط كهربائية", "خرائط صحية", "(الرندر) الإظهار",
                "رسومات التنفيذية", "تشطيب اللوحات والطباعة", "خرائط التبريد والتكييف",
                "خرائط منظومة إطفاء الحرائق", "خرائط منظومة الكميرات", "خرائط التدفئة المركزية"
            ]

    def setup_dialog(self):
        """إعداد الحوار"""
        title = "تعديل المرحلة" if self.is_edit_mode else "إضافة مرحلة جديدة"
        self.setWindowTitle(title)
        self.setGeometry(200, 200, 700, 500)
        self.setLayoutDirection(Qt.RightToLeft)
        self.setModal(True)

    def create_ui(self):
        """إنشاء واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)

        # عنوان النافذة
        title_label = QLabel("بيانات المرحلة")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 10px;
                background-color: #ecf0f1;
                border-radius: 8px;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(title_label)

        # إنشاء نموذج الإدخال
        form_layout = QFormLayout()
        form_layout.setSpacing(12)
        form_layout.setLabelAlignment(Qt.AlignRight)

        # اسم المرحلة
        self.phase_name_combo = QComboBox()
        phases_list = self.get_phases_list_by_type()
        self.phase_name_combo.addItems(phases_list)
        self.phase_name_combo.setEditable(True)
        self.phase_name_combo.setStyleSheet("QComboBox { text-align: center; }")
        form_layout.addRow("اسم المرحلة:", self.phase_name_combo)

        # وصف المرحلة
        self.description_edit = QLineEdit()
        self.description_edit.setAlignment(Qt.AlignCenter)
        form_layout.addRow("وصف المرحلة:", self.description_edit)

        # الوحدة
        self.unit_combo = QComboBox()
        units_list = ["متر مربع", "متر طولي", "قطعة", "مجموعة", "لوحة", "نسخة", "خدمة"]
        self.unit_combo.addItems(units_list)
        self.unit_combo.setEditable(True)
        self.unit_combo.setStyleSheet("QComboBox { text-align: center; }")
        form_layout.addRow("الوحدة:", self.unit_combo)

        # الكمية
        self.quantity_spin = QSpinBox()
        self.quantity_spin.setRange(1, 999999)
        self.quantity_spin.setValue(1)
        self.quantity_spin.setAlignment(Qt.AlignCenter)
        form_layout.addRow("الكمية:", self.quantity_spin)

        # السعر
        self.price_spin = QDoubleSpinBox()
        self.price_spin.setRange(0.01, 999999999.99)
        self.price_spin.setDecimals(2)
        self.price_spin.setValue(100.00)
        self.price_spin.setAlignment(Qt.AlignCenter)
        form_layout.addRow("السعر:", self.price_spin)

        # الإجمالي (للعرض فقط)
        self.total_label = QLabel("0.00")
        self.total_label.setAlignment(Qt.AlignCenter)
        self.total_label.setStyleSheet("""
            QLabel {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 4px;
                padding: 8px;
                font-weight: bold;
                color: #27ae60;
            }
        """)
        form_layout.addRow("الإجمالي:", self.total_label)

        # ربط تحديث الإجمالي
        self.quantity_spin.valueChanged.connect(self.update_total)
        self.price_spin.valueChanged.connect(self.update_total)

        # ملاحظات
        self.notes_edit = QTextEdit()
        self.notes_edit.setMaximumHeight(80)
        form_layout.addRow("ملاحظات:", self.notes_edit)

        # إضافة قسم تعيين المهندس
        engineer_group = QGroupBox("تعيين المهندس المسؤول (اختياري)")
        engineer_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        engineer_layout = QFormLayout(engineer_group)
        engineer_layout.setSpacing(12)
        engineer_layout.setLabelAlignment(Qt.AlignRight)

        # المهندس المسؤول
        self.engineer_combo = QComboBox()
        self.engineer_combo.addItem("-- لا يوجد مهندس مُعيّن --", None)
        self.load_engineers()
        engineer_layout.addRow("المهندس المسؤول:", self.engineer_combo)

        # نسبة المهندس
        self.engineer_percentage_spin = QSpinBox()
        self.engineer_percentage_spin.setRange(0, 100)
        self.engineer_percentage_spin.setValue(0)
        self.engineer_percentage_spin.setSuffix("%")
        self.engineer_percentage_spin.setAlignment(Qt.AlignCenter)
        self.engineer_percentage_spin.valueChanged.connect(self.calculate_engineer_amount)
        engineer_layout.addRow("نسبة المهندس:", self.engineer_percentage_spin)

        # مبلغ المهندس
        self.engineer_amount_spin = QDoubleSpinBox()
        self.engineer_amount_spin.setRange(0, 999999999.99)
        self.engineer_amount_spin.setDecimals(2)
        self.engineer_amount_spin.setValue(0.0)
        self.engineer_amount_spin.setAlignment(Qt.AlignCenter)
        self.engineer_amount_spin.valueChanged.connect(self.calculate_engineer_percentage)
        engineer_layout.addRow("مبلغ المهندس:", self.engineer_amount_spin)

        layout.addLayout(form_layout)
        layout.addWidget(engineer_group)

        # أزرار الإجراءات
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(10)

        # زر الحفظ
        self.save_btn = QPushButton("حفظ")
        self.save_btn.setIcon(qta.icon('fa5s.save'))
        self.save_btn.clicked.connect(self.save_phase)
        self.save_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
                font-size: 14px;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #2ecc71;
            }
        """)
        buttons_layout.addWidget(self.save_btn)

        # زر الإلغاء
        self.cancel_btn = QPushButton("إلغاء")
        self.cancel_btn.setIcon(qta.icon('fa5s.times'))
        self.cancel_btn.clicked.connect(self.reject)
        self.cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
                font-size: 14px;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #bdc3c7;
            }
        """)
        buttons_layout.addWidget(self.cancel_btn)

        layout.addLayout(buttons_layout)

        # تحديث الإجمالي الأولي
        self.update_total()

    def update_total(self):
        """تحديث الإجمالي"""
        quantity = self.quantity_spin.value()
        price = self.price_spin.value()
        total = quantity * price
        self.total_label.setText(f"{total:,.2f}")
        # تحديث مبلغ المهندس إذا كانت النسبة محددة
        self.calculate_engineer_amount()

    def load_engineers(self):
        """تحميل قائمة المهندسين"""
        try:
            conn = mysql.connector.connect(
                host=host, user=user_r, password=password_r,
                database="project_manager_V2"
            )
            cursor = conn.cursor()

            cursor.execute("""
                SELECT id, اسم_الموظف, الوظيفة, النسبة FROM الموظفين
                WHERE الحالة = 'نشط'
                AND (التصنيف = 'مهندس' OR الوظيفة LIKE '%مهندس%')
                AND الوظيفة NOT LIKE '%استقبال%'
                AND الوظيفة NOT LIKE '%موظف%'
                AND الوظيفة NOT LIKE '%عامل%'
                ORDER BY اسم_الموظف
            """)

            engineers = cursor.fetchall()

            for engineer_id, engineer_name, job_title, default_percentage in engineers:
                display_text = f"{engineer_name} - {job_title}" if job_title else engineer_name
                self.engineer_combo.addItem(display_text, engineer_id)
                # حفظ النسبة الافتراضية كبيانات إضافية
                self.engineer_combo.setItemData(self.engineer_combo.count() - 1,
                                              {'id': engineer_id, 'default_percentage': default_percentage or 0},
                                              Qt.UserRole + 1)

            # ربط إشارة تغيير الاختيار بدالة التعبئة التلقائية
            self.engineer_combo.currentIndexChanged.connect(self.on_engineer_changed)

            conn.close()

        except Exception as e:
            print(f"خطأ في تحميل المهندسين: {e}")

    def on_engineer_changed(self):
        """معالج تغيير اختيار المهندس - تعبئة النسبة الافتراضية"""
        try:
            current_data = self.engineer_combo.currentData(Qt.UserRole + 1)
            if current_data and isinstance(current_data, dict):
                default_percentage = current_data.get('default_percentage', 0)
                if default_percentage > 0:
                    self.engineer_percentage_spin.setValue(int(default_percentage))
                    # تحديث المبلغ بناءً على النسبة الجديدة
                    self.calculate_engineer_amount()
        except Exception as e:
            print(f"خطأ في تعبئة النسبة الافتراضية: {e}")

    def calculate_engineer_amount(self):
        """حساب مبلغ المهندس بناءً على النسبة"""
        try:
            percentage = self.engineer_percentage_spin.value()
            if percentage > 0:
                total = self.quantity_spin.value() * self.price_spin.value()
                amount = (total * percentage) / 100
                # تجنب التحديث المتكرر
                self.engineer_amount_spin.blockSignals(True)
                self.engineer_amount_spin.setValue(amount)
                self.engineer_amount_spin.blockSignals(False)
        except Exception as e:
            print(f"خطأ في حساب مبلغ المهندس: {e}")

    def calculate_engineer_percentage(self):
        """حساب نسبة المهندس بناءً على المبلغ"""
        try:
            amount = self.engineer_amount_spin.value()
            total = self.quantity_spin.value() * self.price_spin.value()
            if total > 0 and amount > 0:
                percentage = (amount * 100) / total
                if percentage <= 100:
                    # تجنب التحديث المتكرر
                    self.engineer_percentage_spin.blockSignals(True)
                    self.engineer_percentage_spin.setValue(int(percentage))
                    self.engineer_percentage_spin.blockSignals(False)
                else:
                    # إذا تجاوزت النسبة 100%، اعرض تحذير وأعد تعيين المبلغ
                    QMessageBox.warning(self, "تحذير", "مبلغ المهندس لا يمكن أن يتجاوز إجمالي المرحلة")
                    max_amount = total
                    self.engineer_amount_spin.setValue(max_amount)
                    self.engineer_percentage_spin.setValue(100)
        except Exception as e:
            print(f"خطأ في حساب نسبة المهندس: {e}")

    def load_phase_data(self):
        """تحميل بيانات المرحلة للتعديل"""
        try:
            conn = mysql.connector.connect(
                host=host, user=user_r, password=password_r,
                database="project_manager_V2"
            )
            cursor = conn.cursor()

            # تحميل بيانات المرحلة
            cursor.execute("""
                SELECT اسم_المرحلة, وصف_المرحلة, الوحدة, الكمية, السعر, ملاحظات
                FROM المشاريع_المراحل
                WHERE id = %s
            """, (self.phase_id,))

            result = cursor.fetchone()

            if result:
                phase_name, description, unit, quantity, price, notes = result

                # تعبئة الحقول
                self.phase_name_combo.setCurrentText(phase_name or "")
                self.description_edit.setText(description or "")
                self.unit_combo.setCurrentText(unit or "")
                self.quantity_spin.setValue(quantity or 1)
                self.price_spin.setValue(float(price) if price else 0.0)
                self.notes_edit.setPlainText(notes or "")

                # تحديث الإجمالي
                self.update_total()

            # تحميل بيانات المهندس المُعيّن (إن وجد)
            cursor.execute("""
                SELECT معرف_المهندس, نسبة_المهندس, مبلغ_المهندس
                FROM المشاريع_مهام_المهندسين
                WHERE معرف_المرحلة = %s
                LIMIT 1
            """, (self.phase_id,))

            engineer_result = cursor.fetchone()
            if engineer_result:
                engineer_id, percentage, amount = engineer_result

                # تعيين المهندس
                for i in range(self.engineer_combo.count()):
                    if self.engineer_combo.itemData(i) == engineer_id:
                        self.engineer_combo.setCurrentIndex(i)
                        break

                # تعيين النسبة والمبلغ
                self.engineer_percentage_spin.setValue(percentage or 0)
                self.engineer_amount_spin.setValue(float(amount) if amount else 0.0)

            conn.close()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل بيانات المرحلة: {str(e)}")

    def save_phase(self):
        """حفظ بيانات المرحلة"""
        # التحقق من صحة البيانات
        phase_name = self.phase_name_combo.currentText().strip()
        if not phase_name:
            QMessageBox.warning(self, "تحذير", "يرجى إدخال اسم المرحلة")
            self.phase_name_combo.setFocus()
            return

        description = self.description_edit.text().strip()
        unit = self.unit_combo.currentText().strip()
        quantity = self.quantity_spin.value()
        price = self.price_spin.value()
        notes = self.notes_edit.toPlainText().strip()

        if quantity <= 0:
            QMessageBox.warning(self, "تحذير", "يرجى إدخال كمية أكبر من صفر")
            self.quantity_spin.setFocus()
            return

        if price <= 0:
            QMessageBox.warning(self, "تحذير", "يرجى إدخال سعر أكبر من صفر")
            self.price_spin.setFocus()
            return

        # الحصول على بيانات المهندس
        engineer_id = self.engineer_combo.currentData()
        engineer_percentage = self.engineer_percentage_spin.value()
        engineer_amount = self.engineer_amount_spin.value()

        # التحقق من صحة بيانات المهندس
        if engineer_id and engineer_percentage > 100:
            QMessageBox.warning(self, "تحذير", "نسبة المهندس لا يمكن أن تتجاوز 100%")
            return

        try:
            conn = mysql.connector.connect(
                host=host, user=user_r, password=password_r,
                database="project_manager_V2"
            )
            cursor = conn.cursor()

            # بدء المعاملة
            conn.start_transaction()

            if self.is_edit_mode:
                # تحديث المرحلة الموجودة
                cursor.execute("""
                    UPDATE المشاريع_المراحل
                    SET اسم_المرحلة = %s, وصف_المرحلة = %s, الوحدة = %s,
                        الكمية = %s, السعر = %s, ملاحظات = %s
                    WHERE id = %s
                """, (phase_name, description, unit, quantity, price, notes, self.phase_id))

                action_msg = f"تم تحديث المرحلة '{phase_name}' بنجاح"
                new_phase_id = self.phase_id
            else:
                # إضافة مرحلة جديدة
                cursor.execute("""
                    INSERT INTO المشاريع_المراحل
                    (معرف_المشروع, اسم_المرحلة, وصف_المرحلة, الوحدة, الكمية, السعر, ملاحظات, المستخدم, السنة)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                """, (
                    self.project_id, phase_name, description, unit, quantity, price, notes,
                    "admin", datetime.now().year
                ))

                new_phase_id = cursor.lastrowid
                action_msg = f"تم إضافة المرحلة '{phase_name}' بنجاح"

                # إنشاء مهمة مهندس تلقائياً إذا تم تعيين مهندس
                if engineer_id and not self.is_edit_mode:
                    self.create_engineer_task(cursor, new_phase_id, engineer_id, engineer_percentage, engineer_amount)
                    action_msg += f"\nتم تعيين المهندس للمرحلة تلقائياً"

            # تأكيد المعاملة
            conn.commit()
            conn.close()

            QMessageBox.information(self, "نجح", action_msg)
            self.accept()

        except mysql.connector.IntegrityError as e:
            conn.rollback()
            if "unq_مرحلة_مهندس" in str(e):
                QMessageBox.warning(self, "تحذير", "هذا المهندس مُعيّن بالفعل لهذه المرحلة")
            else:
                QMessageBox.critical(self, "خطأ", f"خطأ في قاعدة البيانات: {str(e)}")
        except Exception as e:
            conn.rollback()
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ المرحلة: {str(e)}")

    def create_engineer_task(self, cursor, phase_id, engineer_id, percentage, amount):
        """إنشاء مهمة مهندس تلقائياً"""
        try:
            from datetime import date

            current_date = date.today()

            cursor.execute("""
                INSERT INTO المشاريع_مهام_المهندسين
                (معرف_المرحلة, معرف_المهندس, نسبة_المهندس, مبلغ_المهندس,
                 حالة_مبلغ_المهندس, تاريخ_البداية, تاريخ_النهاية, الحالة, المستخدم)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
            """, (
                phase_id, engineer_id, percentage, amount,
                "غير مدرج", current_date, current_date, "لم يبدأ", "admin"
            ))

        except Exception as e:
            raise Exception(f"فشل في إنشاء مهمة المهندس: {str(e)}")


# دالة فتح النافذة الجديدة
def open_project_phases_window(parent, project_data, project_type="تصميم"):
    """
    فتح نافذة مراحل المشروع الجديدة

    Args:
        parent: النافذة الأب
        project_data: بيانات المشروع (dict)
        project_type: نوع المشروع (str)

    Returns:
        ProjectPhasesWindow: نافذة مراحل المشروع
    """
    window = ProjectPhasesWindow(parent, project_data, project_type)
    window.show()
    return window
